package main

import (
	"github.com/ethereum/go-ethereum/log"
	"github.com/urfave/cli/v2"
	"wallet-sign-account/config"
	"wallet-sign-account/service"
	"wallet-sign-common/flags"
)

func NewCli() *cli.App {
	return &cli.App{
		Name:    "wallet-sign-account",
		Version: "0.0.1",
		Commands: []*cli.Command{
			{
				Name:   "rpc",
				Action: runRpc,
				Flags: []cli.Flag{
					flags.Config,
				},
			},
		},
	}
}

func runRpc(ctx *cli.Context) error {
	log.Info("Running RPC Server")
	cfg, err := config.NewConfig(ctx.String(flags.Config.Name))
	if err != nil {
		return err
	}
	
	s, err := service.NewRpcService(cfg)
	if err != nil {
		return err
	}

	return s.Start()
}
