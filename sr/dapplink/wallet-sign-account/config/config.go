package config

import (
	"errors"
	"fmt"
	"github.com/samber/lo"
	"golang.org/x/exp/slices"
	config2 "wallet-sign-common/config"
)

type RpcConfig struct {
	Port uint64 `mapstructure:"port"`
}

type NodeConfig struct {
	RpcUrl string `mapstructure:"rpc_url"`
	Enable bool   `mapstructure:"enable"`
}

type Config struct {
	Rpc      config2.RpcConfig      `mapstructure:"rpc"`
	NodesMap map[string]*NodeConfig `mapstructure:"nodes"`
}

func (c *Config) ValidateChains(implementedChains []string) error {
	var err error
	cfgChains := lo.Keys(c.NodesMap)

	unimplementChains := lo.Filter(cfgChains, func(name string, index int) bool {
		return !slices.Contains(implementedChains, name)
	})
	if len(unimplementChains) > 0 {
		err = errors.Join(err, errors.New(fmt.Sprintf("配置文件中指定了未实现的链: %+v", unimplementChains)))
	}

	unconfiguredChains := lo.Filter(implementedChains, func(name string, index int) bool {
		return !slices.Contains(cfgChains, name)
	})
	if len(unimplementChains) > 0 {
		err = errors.Join(err, errors.New(fmt.Sprintf("配置文件中缺少链的配置: %+v", unconfiguredChains)))
	}

	return err
}

func NewConfig(path string) (*Config, error) {
	var config Config
	return config2.ReadConfig(path, &config)
}
