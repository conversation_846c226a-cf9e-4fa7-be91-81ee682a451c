package chain

import (
	"context"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/log"
	"github.com/samber/lo"
	"sync"
	"wallet-sign-account/chain/ethereum"
	"wallet-sign-account/chain/optimism"
	"wallet-sign-account/config"
	accountpb "wallet-sign-protobufs/generated/account"
	commonpb "wallet-sign-protobufs/generated/common"
)

var chainFactoryMap = map[string]func(*config.NodeConfig) (accountpb.AccountServiceServer, error){
	ethereum.GetName(): ethereum.New,
	optimism.GetName(): optimism.New,
}

var chainMap = sync.Map{} //map[string]accountpb.ServiceServer

type Dispatcher struct {
	config          *config.Config
	supportedChains map[string]string
	accountpb.UnimplementedAccountServiceServer
}

func NewDispatcher(cfg *config.Config) (*Dispatcher, error) {
	err := cfg.ValidateChains(lo.Keys(chainFactoryMap))
	if err != nil {
		return nil, err
	}

	unenabledChains := lo.FilterMapToSlice(cfg.NodesMap, func(name string, nodeConfig *config.NodeConfig) (string, bool) {
		return name, !nodeConfig.Enable
	})

	enabledChains := lo.FilterMapToSlice(cfg.NodesMap, func(name string, nodeConfig *config.NodeConfig) (string, bool) {
		return name, nodeConfig.Enable
	})

	log.Info("链已启用", "chains", enabledChains)

	if len(unenabledChains) > 0 {
		log.Warn("链未启用", "chains", unenabledChains)
	}

	var supportedChains = lo.KeyBy(
		lo.Filter(lo.Keys(chainFactoryMap),
			func(name string, index int) bool {
				return cfg.NodesMap[name].Enable
			}),
		func(name string) string {
			return name
		},
	)

	return &Dispatcher{
		config:          cfg,
		supportedChains: supportedChains,
	}, nil
}

func (d *Dispatcher) factory(name string) (accountpb.AccountServiceServer, error) {
	if value, ok := chainMap.Load(name); ok {
		if !lo.HasKey(d.supportedChains, name) {
			return nil, errors.New(fmt.Sprintf("chain %s is not enabled", name))
		}

		return value.(accountpb.AccountServiceServer), nil
	}

	if factory, ok := chainFactoryMap[name]; ok {
		server, err := factory(d.config.NodesMap[name])
		if err != nil {
			return nil, err
		}

		chainMap.Store(name, server)
		return server, nil
	}

	return nil, errors.New(fmt.Sprintf("chain %s is not implemented", name))
}

func (d *Dispatcher) GetSupportChains(context context.Context, req *accountpb.GetSupportChainsRequest) (*accountpb.GetSupportChainsResponse, error) {
	return &accountpb.GetSupportChainsResponse{
		Header: &commonpb.ResponseHeader{
			Code:    commonpb.ReturnCode_SUCCESS,
			Message: "",
		},
		Chains: lo.Keys(d.supportedChains),
	}, nil
}

func (d *Dispatcher) GetBlockByNumber(context context.Context, req *accountpb.GetBlockByNumberRequest) (*accountpb.GetBlockByNumberResponse, error) {
	instance, err := d.factory(req.Header.Chain)
	if err != nil {
		return &accountpb.GetBlockByNumberResponse{
			Header: &commonpb.ResponseHeader{
				Code:    commonpb.ReturnCode_ERROR,
				Message: err.Error(),
			},
		}, nil
	}

	return instance.GetBlockByNumber(context, req)
}

func (d *Dispatcher) PublicKeyToAddress(context context.Context, req *accountpb.PublicKeyToAddressRequest) (*accountpb.PublicKeyToAddressResponse, error) {
	instance, err := d.factory(req.Header.Chain)
	if err != nil {
		return &accountpb.PublicKeyToAddressResponse{
			Header: &commonpb.ResponseHeader{
				Code:    commonpb.ReturnCode_ERROR,
				Message: err.Error(),
			},
		}, nil
	}

	return instance.PublicKeyToAddress(context, req)
}
