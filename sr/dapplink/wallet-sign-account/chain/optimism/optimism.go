package optimism

import (
	"wallet-sign-account/chain/basic"
	"wallet-sign-account/config"
	"wallet-sign-protobufs/generated/account"
)

func GetName() string {
	return "optimism"
}

type Optimism struct {
	*basic.Basic
}

func New(config *config.NodeConfig) (accountpb.AccountServiceServer, error) {
	server, err := basic.New(config)
	if err != nil {
		return nil, err
	}

	return &Optimism{
		Basic: server,
	}, nil
}
