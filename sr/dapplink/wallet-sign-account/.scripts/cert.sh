#!/bin/bash

# -----------------------------------------------------------------------------
# <PERSON><PERSON>t to generate a self-signed TLS certificate for local development.
# -----------------------------------------------------------------------------

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---

# The hostname for the certificate. Uses environment variable or defaults to 'localhost'.
# This is crucial for the browser/client to trust the certificate for that specific name.
HOST="${WALLET_SIGN_GO_HOST:-localhost}"

# Output directory for the generated files.
OUTPUT_DIR="./certs"

# RSA key size. 2048 bits is the recommended minimum for security. 1024 is outdated.
KEY_SIZE=2048

# Certificate validity in days.
DAYS=3650

# --- File Paths ---
PRIVATE_KEY_PATH="$OUTPUT_DIR/private.key"
CERT_PATH="$OUTPUT_DIR/server.crt"


# --- Main Logic ---

# 1. Check for OpenSSL command
if ! command -v openssl &> /dev/null; then
    echo "Error: openssl is not installed. Please install it to continue." >&2
    exit 1
fi

# 2. Create the output directory if it doesn't exist.
mkdir -p "$OUTPUT_DIR"

echo "-> Generating a ${KEY_SIZE}-bit RSA private key..."
openssl genrsa -out "$PRIVATE_KEY_PATH" "$KEY_SIZE"

echo "-> Generating self-signed certificate for host: $HOST"
# -nodes: Don't encrypt the private key (no passphrase).
# -subj: Sets the subject, /CN=Common Name is the primary identity.
# -addext: Adds the modern Subject Alternative Name (SAN) extension, required by modern clients.
openssl req -x509 -new -nodes \
    -key "$PRIVATE_KEY_PATH" \
    -days "$DAYS" \
    -out "$CERT_PATH" \
    -subj "/CN=$HOST" \
    -addext "subjectAltName=DNS:$HOST"

echo
echo "✅ Done."
echo "--------------------------------------------------"
echo "🔑 Private Key:  $PRIVATE_KEY_PATH"
echo "📜 Certificate:    $CERT_PATH"
echo "--------------------------------------------------"
echo "Use these files to configure TLS in your server."
