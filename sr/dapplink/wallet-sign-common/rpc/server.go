package rpc

import (
	"github.com/ethereum/go-ethereum/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/reflection"
	"net"
	"strconv"
	"wallet-sign-common/config"
)

func StartServer(config *config.RpcConfig, beforeStart func(gServer *grpc.Server) error) error {
	log.Info("Starting RPC Server")
	lis, err := net.Listen("tcp", ":"+strconv.Itoa(int(config.Port)))
	if err != nil {
		return err
	}

	var gServer *grpc.Server
	if config.CertFile != "" && config.KeyFile != "" {
		creds, err := credentials.NewServerTLSFromFile(config.CertFile, config.KeyFile)
		if err != nil {
			return err
		}

		gServer = grpc.NewServer(
			grpc.Creds(creds),
		)
		log.Info("TLS 已启用")
	} else {
		gServer = grpc.NewServer()
	}

	defer gServer.GracefulStop()

	if config.Reflection {
		// 启用 gRPC 反射服务
		// 服务发现 - 让客户端能够动态发现服务器上有哪些 gRPC 服务和方法
		// 元数据查询 - 客户端可以查询服务的 wallet-protobuf 定义、方法签名等信息
		// 动态调用 - 无需预先知道 .proto 文件就能调用服务
		log.Info("RPC 反射已启用")
		reflection.Register(gServer)
	}

	err = beforeStart(gServer)
	if err != nil {
		return err
	}

	log.Info("RPC Server is running on :" + lis.Addr().String())
	if err := gServer.Serve(lis); err != nil {
		return err
	}

	return nil
}
