# ====================================================================================
# 变量定义 (Variables)
# ====================================================================================

# Go 相关变量
BINARY_NAME := wallet-sign-account
CMD_PATH    := ./cmd/$(BINARY_NAME)

# Protobuf 相关变量
PROTO_SRC_DIR := ./protobuf
PROTO_FILES   := $(wildcard $(PROTO_SRC_DIR)/*.proto)
PROTO_OUT_DIR := ./protobuf/generated

# 生成的文件（基于实际的 protoc 命名规则）
GENERATED_GO_FILES   := $(patsubst $(PROTO_SRC_DIR)/%.proto,$(PROTO_OUT_DIR)/%.pb.go,$(PROTO_FILES))
GENERATED_GRPC_FILES := $(patsubst $(PROTO_SRC_DIR)/%.proto,$(PROTO_OUT_DIR)/%_grpc.pb.go,$(PROTO_FILES))
ALL_GENERATED_FILES  := $(GENERATED_GO_FILES) $(GENERATED_GRPC_FILES)

# ====================================================================================
# 指令目标 (Targets)
# ====================================================================================

# 设置默认目标为 help，这样直接运行 `make` 就会显示帮助信息
.DEFAULT_GOAL := help

# .PHONY 用于声明伪目标，这些目标不代表文件名，避免与同名文件冲突
.PHONY: help dev build protobuf ui test cert clean

# help 目标用于打印帮助信息，方便快速了解所有可用指令
help:
	@echo "Usage: make <target>"
	@echo ""
	@echo "Targets:"
	@echo "  help        显示此帮助信息"
	@echo "  dev         以开发模式运行应用"
	@echo "  build       编译生成应用二进制文件"
	@echo "  protobuf    从 protobuf 定义生成 Go 代码 (仅当文件变更时)"
	@echo "  ui          运行 UI 脚本"
	@echo "  test        运行所有测试"
	@echo "  cert        生成 TLS 证书"
	@echo "  clean       清理生成的文件和编译产物"

# 运行开发服务器
dev:
	@echo "-> 正在以开发模式运行应用..."
	go run $(CMD_PATH) rpc

# 编译应用
build:
	@echo "-> 正在编译应用..."
	go build -o $(BINARY_NAME) $(CMD_PATH)

# protobuf 的主目标，它依赖于所有生成的文件
protobuf: $(ALL_GENERATED_FILES)

# 确保输出目录存在
$(PROTO_OUT_DIR):
	@echo "-> 创建 protobuf 输出目录..."
	mkdir -p $(PROTO_OUT_DIR)

# 生成规则：当任何 .proto 文件变更时重新生成所有文件
$(ALL_GENERATED_FILES): $(PROTO_FILES) | $(PROTO_OUT_DIR)
	@echo "-> Protobuf 源文件已变更，正在重新生成代码..."
	@echo "-> 处理文件: $(PROTO_FILES)"
		protoc -I=. -I=.. -I=$(PROTO_SRC_DIR) --go_out=.. --go-grpc_out=.. $(PROTO_FILES)

# 运行 UI 脚本
ui:
	@echo "-> 正在运行 UI 脚本..."
	sh ./.scripts/ui.sh

# 运行测试
test:
	@echo "-> 正在运行测试..."
	go test -count=1 ./...

# 生成证书
cert:
	@echo "-> 正在生成证书..."
	sh ./.scripts/cert.sh

# 清理工作区
clean:
	@echo "-> 正在清理工作区..."
	rm -f $(BINARY_NAME)
	rm -rf $(PROTO_OUT_DIR)
	@echo "-> 清理完成"

# 显示当前配置信息（调试用）
info:
	@echo "PROTO_FILES: $(PROTO_FILES)"
	@echo "GENERATED_GO_FILES: $(GENERATED_GO_FILES)"
	@echo "GENERATED_GRPC_FILES: $(GENERATED_GRPC_FILES)"
	@echo "ALL_GENERATED_FILES: $(ALL_GENERATED_FILES)"
