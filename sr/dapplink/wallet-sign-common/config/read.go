package config

import (
	"fmt"
	"github.com/spf13/viper"
)

func ReadConfig[T any](path string, config T) (T, error) {
	vp := viper.New()
	vp.SetConfigFile(path) // 文件名(不带路径，不带后缀)

	if err := vp.ReadInConfig(); err != nil {
		return config, fmt.Errorf("读取配置文件%s出错:%s", path, err)
	}

	err := vp.Unmarshal(&config)
	if err != nil {
		return config, fmt.Errorf("解析配置文件%s出错:%s", path, err)
	}

	return config, nil
}
