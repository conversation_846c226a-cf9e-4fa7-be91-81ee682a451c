syntax = "proto3";
package signer;
import "common.proto";
option go_package="wallet-sign-protobufs/generated/signer;signerpb";

service SignerService {
//  rpc getSupportSignWays(GetSupportSignWaysRequest) returns (GetSupportSignWaysResponse) {}
  rpc exportPublicKeyList(ExportPublicKeyListRequest) returns (ExportPublicKeyListResponse) {}
  // 公钥转地址
  rpc PublicKeyToAddress(PublicKeyToAddressRequest) returns(PublicKeyToAddressResponse){}
  rpc signTxMessage(SignTxMessageRequest) returns (SignTxMessageResponse) {}
  rpc signTxMessageInBatches(SignTxMessageInBatchesRequest) returns (SignTxMessageInBatchesResponse) {}
}

message GetSupportSignWaysRequest {
  common.RequestHeader header = 1;
}

message GetSupportSignWaysResponse{
    common.ResponseHeader header = 1;
    repeated string sign_ways = 3;
}

message ExportPublicKeyListRequest {
  common.RequestHeader header = 1;
  uint64 count = 3;
}

message ExportPublicKeyListResponse {
  common.ResponseHeader header = 1;
  repeated PublicKey list = 3;
}

message PublicKey {
  string public_key = 1;
  string compressed_publicKey = 2;
}

message SignTxMessageRequest {
  common.RequestHeader header = 1;
  string tx_body_base64 = 2;
  string public_key = 3;
}

message SignTxMessageResponse {
  common.ResponseHeader header = 1;
  string signed_message = 2;
  string tx_hash = 3;
  string tx_message_hash = 4;
}

message SignTxMessageInBatchesRequest {
  common.RequestHeader header = 1;
  repeated  SignTxMessageRequest list = 2;
}

message SignTxMessageInBatchesResponse {
  common.ResponseHeader header = 1;
  repeated SignTxMessageResponse signed_tx_messages = 2;
}

message PublicKeyToAddressRequest {
  common.RequestHeader header = 1;
  string public_key = 2;
}
message PublicKeyToAddressResponse {
  common.ResponseHeader header = 1;
  string address = 2;
}

// ============= tx body =====================

message EthereumTxBody {
  uint64 nonce = 1;
  string gas_price = 2;
  uint64 gas_limit = 3;
  string max_fee_per_gas = 4;
  string max_priority_fee_per_gas = 5;
  string to = 6;
  string amount = 7;
  string data = 8;
  string chain_id = 9;
  string contract_address = 10;
}

// ===========================================