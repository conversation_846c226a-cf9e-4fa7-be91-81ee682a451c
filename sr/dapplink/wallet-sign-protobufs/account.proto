syntax = "proto3";
package account;
import "common.proto";
option go_package="wallet-sign-protobufs/generated/account;accountpb";

service AccountService {
  // 获取支持的链
  rpc GetSupportChains(GetSupportChainsRequest) returns (GetSupportChainsResponse) {}
  // 获取区块信息
  rpc GetBlockByNumber(GetBlockByNumberRequest) returns (GetBlockByNumberResponse) {}
  // 公钥转地址
  rpc PublicKeyToAddress(PublicKeyToAddressRequest) returns(PublicKeyToAddressResponse){}
}

message GetBlockByNumberRequest {
  common.RequestHeader header = 1;
  uint64 number = 2;
  bool   include_txs = 3;
}
message GetBlockByNumberResponse {
  common.ResponseHeader header = 1;
  uint64 number = 2;
  string hash = 3;
  string parent_hash = 4;
}

message GetSupportChainsRequest {
  common.RequestHeader header = 1;
}
message GetSupportChainsResponse {
  common.ResponseHeader header = 1;
  repeated string chains = 2;
}

message PublicKeyToAddressRequest {
  common.RequestHeader header = 1;
  string public_key = 2;
}
message PublicKeyToAddressResponse {
  common.ResponseHeader header = 1;
  string address = 2;
}
