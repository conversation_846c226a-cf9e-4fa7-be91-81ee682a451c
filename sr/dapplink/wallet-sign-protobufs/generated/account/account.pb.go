// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: account.proto

package accountpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
	common "wallet-sign-protobufs/generated/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetBlockByNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.RequestHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Number        uint64                 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
	IncludeTxs    bool                   `protobuf:"varint,3,opt,name=include_txs,json=includeTxs,proto3" json:"include_txs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockByNumberRequest) Reset() {
	*x = GetBlockByNumberRequest{}
	mi := &file_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockByNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockByNumberRequest) ProtoMessage() {}

func (x *GetBlockByNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockByNumberRequest.ProtoReflect.Descriptor instead.
func (*GetBlockByNumberRequest) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{0}
}

func (x *GetBlockByNumberRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBlockByNumberRequest) GetNumber() uint64 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *GetBlockByNumberRequest) GetIncludeTxs() bool {
	if x != nil {
		return x.IncludeTxs
	}
	return false
}

type GetBlockByNumberResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Number        uint64                 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	ParentHash    string                 `protobuf:"bytes,4,opt,name=parent_hash,json=parentHash,proto3" json:"parent_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockByNumberResponse) Reset() {
	*x = GetBlockByNumberResponse{}
	mi := &file_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockByNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockByNumberResponse) ProtoMessage() {}

func (x *GetBlockByNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockByNumberResponse.ProtoReflect.Descriptor instead.
func (*GetBlockByNumberResponse) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{1}
}

func (x *GetBlockByNumberResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBlockByNumberResponse) GetNumber() uint64 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *GetBlockByNumberResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *GetBlockByNumberResponse) GetParentHash() string {
	if x != nil {
		return x.ParentHash
	}
	return ""
}

type GetSupportChainsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.RequestHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportChainsRequest) Reset() {
	*x = GetSupportChainsRequest{}
	mi := &file_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportChainsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportChainsRequest) ProtoMessage() {}

func (x *GetSupportChainsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportChainsRequest.ProtoReflect.Descriptor instead.
func (*GetSupportChainsRequest) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{2}
}

func (x *GetSupportChainsRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetSupportChainsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Chains        []string               `protobuf:"bytes,2,rep,name=chains,proto3" json:"chains,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportChainsResponse) Reset() {
	*x = GetSupportChainsResponse{}
	mi := &file_account_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportChainsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportChainsResponse) ProtoMessage() {}

func (x *GetSupportChainsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportChainsResponse.ProtoReflect.Descriptor instead.
func (*GetSupportChainsResponse) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{3}
}

func (x *GetSupportChainsResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetSupportChainsResponse) GetChains() []string {
	if x != nil {
		return x.Chains
	}
	return nil
}

type PublicKeyToAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.RequestHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	PublicKey     string                 `protobuf:"bytes,2,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicKeyToAddressRequest) Reset() {
	*x = PublicKeyToAddressRequest{}
	mi := &file_account_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKeyToAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKeyToAddressRequest) ProtoMessage() {}

func (x *PublicKeyToAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKeyToAddressRequest.ProtoReflect.Descriptor instead.
func (*PublicKeyToAddressRequest) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{4}
}

func (x *PublicKeyToAddressRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PublicKeyToAddressRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type PublicKeyToAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Address       string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicKeyToAddressResponse) Reset() {
	*x = PublicKeyToAddressResponse{}
	mi := &file_account_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKeyToAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKeyToAddressResponse) ProtoMessage() {}

func (x *PublicKeyToAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKeyToAddressResponse.ProtoReflect.Descriptor instead.
func (*PublicKeyToAddressResponse) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{5}
}

func (x *PublicKeyToAddressResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PublicKeyToAddressResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_account_proto protoreflect.FileDescriptor

const file_account_proto_rawDesc = "" +
	"\n" +
	"\raccount.proto\x12\aaccount\x1a\fcommon.proto\"\x81\x01\n" +
	"\x17GetBlockByNumberRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\x12\x16\n" +
	"\x06number\x18\x02 \x01(\x04R\x06number\x12\x1f\n" +
	"\vinclude_txs\x18\x03 \x01(\bR\n" +
	"includeTxs\"\x97\x01\n" +
	"\x18GetBlockByNumberResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12\x16\n" +
	"\x06number\x18\x02 \x01(\x04R\x06number\x12\x12\n" +
	"\x04hash\x18\x03 \x01(\tR\x04hash\x12\x1f\n" +
	"\vparent_hash\x18\x04 \x01(\tR\n" +
	"parentHash\"H\n" +
	"\x17GetSupportChainsRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\"b\n" +
	"\x18GetSupportChainsResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12\x16\n" +
	"\x06chains\x18\x02 \x03(\tR\x06chains\"i\n" +
	"\x19PublicKeyToAddressRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\x12\x1d\n" +
	"\n" +
	"public_key\x18\x02 \x01(\tR\tpublicKey\"f\n" +
	"\x1aPublicKeyToAddressResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress2\xa7\x02\n" +
	"\x0eAccountService\x12Y\n" +
	"\x10GetSupportChains\x12 .account.GetSupportChainsRequest\x1a!.account.GetSupportChainsResponse\"\x00\x12Y\n" +
	"\x10GetBlockByNumber\x12 .account.GetBlockByNumberRequest\x1a!.account.GetBlockByNumberResponse\"\x00\x12_\n" +
	"\x12PublicKeyToAddress\x12\".account.PublicKeyToAddressRequest\x1a#.account.PublicKeyToAddressResponse\"\x00B3Z1wallet-sign-protobufs/generated/account;accountpbb\x06proto3"

var (
	file_account_proto_rawDescOnce sync.Once
	file_account_proto_rawDescData []byte
)

func file_account_proto_rawDescGZIP() []byte {
	file_account_proto_rawDescOnce.Do(func() {
		file_account_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_account_proto_rawDesc), len(file_account_proto_rawDesc)))
	})
	return file_account_proto_rawDescData
}

var file_account_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_account_proto_goTypes = []any{
	(*GetBlockByNumberRequest)(nil),    // 0: account.GetBlockByNumberRequest
	(*GetBlockByNumberResponse)(nil),   // 1: account.GetBlockByNumberResponse
	(*GetSupportChainsRequest)(nil),    // 2: account.GetSupportChainsRequest
	(*GetSupportChainsResponse)(nil),   // 3: account.GetSupportChainsResponse
	(*PublicKeyToAddressRequest)(nil),  // 4: account.PublicKeyToAddressRequest
	(*PublicKeyToAddressResponse)(nil), // 5: account.PublicKeyToAddressResponse
	(*common.RequestHeader)(nil),       // 6: common.RequestHeader
	(*common.ResponseHeader)(nil),      // 7: common.ResponseHeader
}
var file_account_proto_depIdxs = []int32{
	6, // 0: account.GetBlockByNumberRequest.header:type_name -> common.RequestHeader
	7, // 1: account.GetBlockByNumberResponse.header:type_name -> common.ResponseHeader
	6, // 2: account.GetSupportChainsRequest.header:type_name -> common.RequestHeader
	7, // 3: account.GetSupportChainsResponse.header:type_name -> common.ResponseHeader
	6, // 4: account.PublicKeyToAddressRequest.header:type_name -> common.RequestHeader
	7, // 5: account.PublicKeyToAddressResponse.header:type_name -> common.ResponseHeader
	2, // 6: account.AccountService.GetSupportChains:input_type -> account.GetSupportChainsRequest
	0, // 7: account.AccountService.GetBlockByNumber:input_type -> account.GetBlockByNumberRequest
	4, // 8: account.AccountService.PublicKeyToAddress:input_type -> account.PublicKeyToAddressRequest
	3, // 9: account.AccountService.GetSupportChains:output_type -> account.GetSupportChainsResponse
	1, // 10: account.AccountService.GetBlockByNumber:output_type -> account.GetBlockByNumberResponse
	5, // 11: account.AccountService.PublicKeyToAddress:output_type -> account.PublicKeyToAddressResponse
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_account_proto_init() }
func file_account_proto_init() {
	if File_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_account_proto_rawDesc), len(file_account_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_account_proto_goTypes,
		DependencyIndexes: file_account_proto_depIdxs,
		MessageInfos:      file_account_proto_msgTypes,
	}.Build()
	File_account_proto = out.File
	file_account_proto_goTypes = nil
	file_account_proto_depIdxs = nil
}
