// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: signer.proto

package signerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SignerService_ExportPublicKeyList_FullMethodName    = "/signer.SignerService/exportPublicKeyList"
	SignerService_PublicKeyToAddress_FullMethodName     = "/signer.SignerService/PublicKeyToAddress"
	SignerService_SignTxMessage_FullMethodName          = "/signer.SignerService/signTxMessage"
	SignerService_SignTxMessageInBatches_FullMethodName = "/signer.SignerService/signTxMessageInBatches"
)

// SignerServiceClient is the client API for SignerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SignerServiceClient interface {
	// rpc getSupportSignWays(GetSupportSignWaysRequest) returns (GetSupportSignWaysResponse) {}
	ExportPublicKeyList(ctx context.Context, in *ExportPublicKeyListRequest, opts ...grpc.CallOption) (*ExportPublicKeyListResponse, error)
	// 公钥转地址
	PublicKeyToAddress(ctx context.Context, in *PublicKeyToAddressRequest, opts ...grpc.CallOption) (*PublicKeyToAddressResponse, error)
	SignTxMessage(ctx context.Context, in *SignTxMessageRequest, opts ...grpc.CallOption) (*SignTxMessageResponse, error)
	SignTxMessageInBatches(ctx context.Context, in *SignTxMessageInBatchesRequest, opts ...grpc.CallOption) (*SignTxMessageInBatchesResponse, error)
}

type signerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSignerServiceClient(cc grpc.ClientConnInterface) SignerServiceClient {
	return &signerServiceClient{cc}
}

func (c *signerServiceClient) ExportPublicKeyList(ctx context.Context, in *ExportPublicKeyListRequest, opts ...grpc.CallOption) (*ExportPublicKeyListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExportPublicKeyListResponse)
	err := c.cc.Invoke(ctx, SignerService_ExportPublicKeyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signerServiceClient) PublicKeyToAddress(ctx context.Context, in *PublicKeyToAddressRequest, opts ...grpc.CallOption) (*PublicKeyToAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PublicKeyToAddressResponse)
	err := c.cc.Invoke(ctx, SignerService_PublicKeyToAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signerServiceClient) SignTxMessage(ctx context.Context, in *SignTxMessageRequest, opts ...grpc.CallOption) (*SignTxMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignTxMessageResponse)
	err := c.cc.Invoke(ctx, SignerService_SignTxMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signerServiceClient) SignTxMessageInBatches(ctx context.Context, in *SignTxMessageInBatchesRequest, opts ...grpc.CallOption) (*SignTxMessageInBatchesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignTxMessageInBatchesResponse)
	err := c.cc.Invoke(ctx, SignerService_SignTxMessageInBatches_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SignerServiceServer is the server API for SignerService service.
// All implementations must embed UnimplementedSignerServiceServer
// for forward compatibility.
type SignerServiceServer interface {
	// rpc getSupportSignWays(GetSupportSignWaysRequest) returns (GetSupportSignWaysResponse) {}
	ExportPublicKeyList(context.Context, *ExportPublicKeyListRequest) (*ExportPublicKeyListResponse, error)
	// 公钥转地址
	PublicKeyToAddress(context.Context, *PublicKeyToAddressRequest) (*PublicKeyToAddressResponse, error)
	SignTxMessage(context.Context, *SignTxMessageRequest) (*SignTxMessageResponse, error)
	SignTxMessageInBatches(context.Context, *SignTxMessageInBatchesRequest) (*SignTxMessageInBatchesResponse, error)
	mustEmbedUnimplementedSignerServiceServer()
}

// UnimplementedSignerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSignerServiceServer struct{}

func (UnimplementedSignerServiceServer) ExportPublicKeyList(context.Context, *ExportPublicKeyListRequest) (*ExportPublicKeyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportPublicKeyList not implemented")
}
func (UnimplementedSignerServiceServer) PublicKeyToAddress(context.Context, *PublicKeyToAddressRequest) (*PublicKeyToAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublicKeyToAddress not implemented")
}
func (UnimplementedSignerServiceServer) SignTxMessage(context.Context, *SignTxMessageRequest) (*SignTxMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignTxMessage not implemented")
}
func (UnimplementedSignerServiceServer) SignTxMessageInBatches(context.Context, *SignTxMessageInBatchesRequest) (*SignTxMessageInBatchesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignTxMessageInBatches not implemented")
}
func (UnimplementedSignerServiceServer) mustEmbedUnimplementedSignerServiceServer() {}
func (UnimplementedSignerServiceServer) testEmbeddedByValue()                       {}

// UnsafeSignerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SignerServiceServer will
// result in compilation errors.
type UnsafeSignerServiceServer interface {
	mustEmbedUnimplementedSignerServiceServer()
}

func RegisterSignerServiceServer(s grpc.ServiceRegistrar, srv SignerServiceServer) {
	// If the following call pancis, it indicates UnimplementedSignerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SignerService_ServiceDesc, srv)
}

func _SignerService_ExportPublicKeyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportPublicKeyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignerServiceServer).ExportPublicKeyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignerService_ExportPublicKeyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignerServiceServer).ExportPublicKeyList(ctx, req.(*ExportPublicKeyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignerService_PublicKeyToAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublicKeyToAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignerServiceServer).PublicKeyToAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignerService_PublicKeyToAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignerServiceServer).PublicKeyToAddress(ctx, req.(*PublicKeyToAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignerService_SignTxMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignTxMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignerServiceServer).SignTxMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignerService_SignTxMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignerServiceServer).SignTxMessage(ctx, req.(*SignTxMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignerService_SignTxMessageInBatches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignTxMessageInBatchesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignerServiceServer).SignTxMessageInBatches(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignerService_SignTxMessageInBatches_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignerServiceServer).SignTxMessageInBatches(ctx, req.(*SignTxMessageInBatchesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SignerService_ServiceDesc is the grpc.ServiceDesc for SignerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SignerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "signer.SignerService",
	HandlerType: (*SignerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "exportPublicKeyList",
			Handler:    _SignerService_ExportPublicKeyList_Handler,
		},
		{
			MethodName: "PublicKeyToAddress",
			Handler:    _SignerService_PublicKeyToAddress_Handler,
		},
		{
			MethodName: "signTxMessage",
			Handler:    _SignerService_SignTxMessage_Handler,
		},
		{
			MethodName: "signTxMessageInBatches",
			Handler:    _SignerService_SignTxMessageInBatches_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "signer.proto",
}
