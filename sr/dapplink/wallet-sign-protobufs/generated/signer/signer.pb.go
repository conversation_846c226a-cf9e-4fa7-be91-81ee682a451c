// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: signer.proto

package signerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
	common "wallet-sign-protobufs/generated/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSupportSignWaysRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.RequestHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportSignWaysRequest) Reset() {
	*x = GetSupportSignWaysRequest{}
	mi := &file_signer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportSignWaysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportSignWaysRequest) ProtoMessage() {}

func (x *GetSupportSignWaysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportSignWaysRequest.ProtoReflect.Descriptor instead.
func (*GetSupportSignWaysRequest) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{0}
}

func (x *GetSupportSignWaysRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetSupportSignWaysResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SignWays      []string               `protobuf:"bytes,3,rep,name=sign_ways,json=signWays,proto3" json:"sign_ways,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportSignWaysResponse) Reset() {
	*x = GetSupportSignWaysResponse{}
	mi := &file_signer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportSignWaysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportSignWaysResponse) ProtoMessage() {}

func (x *GetSupportSignWaysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportSignWaysResponse.ProtoReflect.Descriptor instead.
func (*GetSupportSignWaysResponse) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{1}
}

func (x *GetSupportSignWaysResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetSupportSignWaysResponse) GetSignWays() []string {
	if x != nil {
		return x.SignWays
	}
	return nil
}

type ExportPublicKeyListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.RequestHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Count         uint64                 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExportPublicKeyListRequest) Reset() {
	*x = ExportPublicKeyListRequest{}
	mi := &file_signer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportPublicKeyListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPublicKeyListRequest) ProtoMessage() {}

func (x *ExportPublicKeyListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPublicKeyListRequest.ProtoReflect.Descriptor instead.
func (*ExportPublicKeyListRequest) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{2}
}

func (x *ExportPublicKeyListRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ExportPublicKeyListRequest) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ExportPublicKeyListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	List          []*PublicKey           `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExportPublicKeyListResponse) Reset() {
	*x = ExportPublicKeyListResponse{}
	mi := &file_signer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportPublicKeyListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPublicKeyListResponse) ProtoMessage() {}

func (x *ExportPublicKeyListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPublicKeyListResponse.ProtoReflect.Descriptor instead.
func (*ExportPublicKeyListResponse) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{3}
}

func (x *ExportPublicKeyListResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ExportPublicKeyListResponse) GetList() []*PublicKey {
	if x != nil {
		return x.List
	}
	return nil
}

type PublicKey struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	PublicKey           string                 `protobuf:"bytes,1,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	CompressedPublicKey string                 `protobuf:"bytes,2,opt,name=compressed_publicKey,json=compressedPublicKey,proto3" json:"compressed_publicKey,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PublicKey) Reset() {
	*x = PublicKey{}
	mi := &file_signer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKey) ProtoMessage() {}

func (x *PublicKey) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKey.ProtoReflect.Descriptor instead.
func (*PublicKey) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{4}
}

func (x *PublicKey) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *PublicKey) GetCompressedPublicKey() string {
	if x != nil {
		return x.CompressedPublicKey
	}
	return ""
}

type SignTxMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.RequestHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TxBodyBase64  string                 `protobuf:"bytes,2,opt,name=tx_body_base64,json=txBodyBase64,proto3" json:"tx_body_base64,omitempty"`
	PublicKey     string                 `protobuf:"bytes,3,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignTxMessageRequest) Reset() {
	*x = SignTxMessageRequest{}
	mi := &file_signer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageRequest) ProtoMessage() {}

func (x *SignTxMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageRequest.ProtoReflect.Descriptor instead.
func (*SignTxMessageRequest) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{5}
}

func (x *SignTxMessageRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SignTxMessageRequest) GetTxBodyBase64() string {
	if x != nil {
		return x.TxBodyBase64
	}
	return ""
}

func (x *SignTxMessageRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type SignTxMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SignedMessage string                 `protobuf:"bytes,2,opt,name=signed_message,json=signedMessage,proto3" json:"signed_message,omitempty"`
	TxHash        string                 `protobuf:"bytes,3,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	TxMessageHash string                 `protobuf:"bytes,4,opt,name=tx_message_hash,json=txMessageHash,proto3" json:"tx_message_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignTxMessageResponse) Reset() {
	*x = SignTxMessageResponse{}
	mi := &file_signer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageResponse) ProtoMessage() {}

func (x *SignTxMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageResponse.ProtoReflect.Descriptor instead.
func (*SignTxMessageResponse) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{6}
}

func (x *SignTxMessageResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SignTxMessageResponse) GetSignedMessage() string {
	if x != nil {
		return x.SignedMessage
	}
	return ""
}

func (x *SignTxMessageResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *SignTxMessageResponse) GetTxMessageHash() string {
	if x != nil {
		return x.TxMessageHash
	}
	return ""
}

type SignTxMessageInBatchesRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Header        *common.RequestHeader   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	List          []*SignTxMessageRequest `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignTxMessageInBatchesRequest) Reset() {
	*x = SignTxMessageInBatchesRequest{}
	mi := &file_signer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageInBatchesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageInBatchesRequest) ProtoMessage() {}

func (x *SignTxMessageInBatchesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageInBatchesRequest.ProtoReflect.Descriptor instead.
func (*SignTxMessageInBatchesRequest) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{7}
}

func (x *SignTxMessageInBatchesRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SignTxMessageInBatchesRequest) GetList() []*SignTxMessageRequest {
	if x != nil {
		return x.List
	}
	return nil
}

type SignTxMessageInBatchesResponse struct {
	state            protoimpl.MessageState   `protogen:"open.v1"`
	Header           *common.ResponseHeader   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SignedTxMessages []*SignTxMessageResponse `protobuf:"bytes,2,rep,name=signed_tx_messages,json=signedTxMessages,proto3" json:"signed_tx_messages,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SignTxMessageInBatchesResponse) Reset() {
	*x = SignTxMessageInBatchesResponse{}
	mi := &file_signer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageInBatchesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageInBatchesResponse) ProtoMessage() {}

func (x *SignTxMessageInBatchesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageInBatchesResponse.ProtoReflect.Descriptor instead.
func (*SignTxMessageInBatchesResponse) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{8}
}

func (x *SignTxMessageInBatchesResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SignTxMessageInBatchesResponse) GetSignedTxMessages() []*SignTxMessageResponse {
	if x != nil {
		return x.SignedTxMessages
	}
	return nil
}

type PublicKeyToAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.RequestHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	PublicKey     string                 `protobuf:"bytes,2,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicKeyToAddressRequest) Reset() {
	*x = PublicKeyToAddressRequest{}
	mi := &file_signer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKeyToAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKeyToAddressRequest) ProtoMessage() {}

func (x *PublicKeyToAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKeyToAddressRequest.ProtoReflect.Descriptor instead.
func (*PublicKeyToAddressRequest) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{9}
}

func (x *PublicKeyToAddressRequest) GetHeader() *common.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PublicKeyToAddressRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type PublicKeyToAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *common.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Address       string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicKeyToAddressResponse) Reset() {
	*x = PublicKeyToAddressResponse{}
	mi := &file_signer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKeyToAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKeyToAddressResponse) ProtoMessage() {}

func (x *PublicKeyToAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKeyToAddressResponse.ProtoReflect.Descriptor instead.
func (*PublicKeyToAddressResponse) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{10}
}

func (x *PublicKeyToAddressResponse) GetHeader() *common.ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PublicKeyToAddressResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type EthereumTxBody struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Nonce                uint64                 `protobuf:"varint,1,opt,name=nonce,proto3" json:"nonce,omitempty"`
	GasPrice             string                 `protobuf:"bytes,2,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
	GasLimit             uint64                 `protobuf:"varint,3,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	MaxFeePerGas         string                 `protobuf:"bytes,4,opt,name=max_fee_per_gas,json=maxFeePerGas,proto3" json:"max_fee_per_gas,omitempty"`
	MaxPriorityFeePerGas string                 `protobuf:"bytes,5,opt,name=max_priority_fee_per_gas,json=maxPriorityFeePerGas,proto3" json:"max_priority_fee_per_gas,omitempty"`
	To                   string                 `protobuf:"bytes,6,opt,name=to,proto3" json:"to,omitempty"`
	Amount               string                 `protobuf:"bytes,7,opt,name=amount,proto3" json:"amount,omitempty"`
	Data                 string                 `protobuf:"bytes,8,opt,name=data,proto3" json:"data,omitempty"`
	ChainId              string                 `protobuf:"bytes,9,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	ContractAddress      string                 `protobuf:"bytes,10,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *EthereumTxBody) Reset() {
	*x = EthereumTxBody{}
	mi := &file_signer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EthereumTxBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EthereumTxBody) ProtoMessage() {}

func (x *EthereumTxBody) ProtoReflect() protoreflect.Message {
	mi := &file_signer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EthereumTxBody.ProtoReflect.Descriptor instead.
func (*EthereumTxBody) Descriptor() ([]byte, []int) {
	return file_signer_proto_rawDescGZIP(), []int{11}
}

func (x *EthereumTxBody) GetNonce() uint64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *EthereumTxBody) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

func (x *EthereumTxBody) GetGasLimit() uint64 {
	if x != nil {
		return x.GasLimit
	}
	return 0
}

func (x *EthereumTxBody) GetMaxFeePerGas() string {
	if x != nil {
		return x.MaxFeePerGas
	}
	return ""
}

func (x *EthereumTxBody) GetMaxPriorityFeePerGas() string {
	if x != nil {
		return x.MaxPriorityFeePerGas
	}
	return ""
}

func (x *EthereumTxBody) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *EthereumTxBody) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *EthereumTxBody) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *EthereumTxBody) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *EthereumTxBody) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

var File_signer_proto protoreflect.FileDescriptor

const file_signer_proto_rawDesc = "" +
	"\n" +
	"\fsigner.proto\x12\x06signer\x1a\fcommon.proto\"J\n" +
	"\x19GetSupportSignWaysRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\"i\n" +
	"\x1aGetSupportSignWaysResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12\x1b\n" +
	"\tsign_ways\x18\x03 \x03(\tR\bsignWays\"a\n" +
	"\x1aExportPublicKeyListRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x04R\x05count\"t\n" +
	"\x1bExportPublicKeyListResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12%\n" +
	"\x04list\x18\x03 \x03(\v2\x11.signer.PublicKeyR\x04list\"]\n" +
	"\tPublicKey\x12\x1d\n" +
	"\n" +
	"public_key\x18\x01 \x01(\tR\tpublicKey\x121\n" +
	"\x14compressed_publicKey\x18\x02 \x01(\tR\x13compressedPublicKey\"\x8a\x01\n" +
	"\x14SignTxMessageRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\x12$\n" +
	"\x0etx_body_base64\x18\x02 \x01(\tR\ftxBodyBase64\x12\x1d\n" +
	"\n" +
	"public_key\x18\x03 \x01(\tR\tpublicKey\"\xaf\x01\n" +
	"\x15SignTxMessageResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12%\n" +
	"\x0esigned_message\x18\x02 \x01(\tR\rsignedMessage\x12\x17\n" +
	"\atx_hash\x18\x03 \x01(\tR\x06txHash\x12&\n" +
	"\x0ftx_message_hash\x18\x04 \x01(\tR\rtxMessageHash\"\x80\x01\n" +
	"\x1dSignTxMessageInBatchesRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\x120\n" +
	"\x04list\x18\x02 \x03(\v2\x1c.signer.SignTxMessageRequestR\x04list\"\x9d\x01\n" +
	"\x1eSignTxMessageInBatchesResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12K\n" +
	"\x12signed_tx_messages\x18\x02 \x03(\v2\x1d.signer.SignTxMessageResponseR\x10signedTxMessages\"i\n" +
	"\x19PublicKeyToAddressRequest\x12-\n" +
	"\x06header\x18\x01 \x01(\v2\x15.common.RequestHeaderR\x06header\x12\x1d\n" +
	"\n" +
	"public_key\x18\x02 \x01(\tR\tpublicKey\"f\n" +
	"\x1aPublicKeyToAddressResponse\x12.\n" +
	"\x06header\x18\x01 \x01(\v2\x16.common.ResponseHeaderR\x06header\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\"\xc1\x02\n" +
	"\x0eEthereumTxBody\x12\x14\n" +
	"\x05nonce\x18\x01 \x01(\x04R\x05nonce\x12\x1b\n" +
	"\tgas_price\x18\x02 \x01(\tR\bgasPrice\x12\x1b\n" +
	"\tgas_limit\x18\x03 \x01(\x04R\bgasLimit\x12%\n" +
	"\x0fmax_fee_per_gas\x18\x04 \x01(\tR\fmaxFeePerGas\x126\n" +
	"\x18max_priority_fee_per_gas\x18\x05 \x01(\tR\x14maxPriorityFeePerGas\x12\x0e\n" +
	"\x02to\x18\x06 \x01(\tR\x02to\x12\x16\n" +
	"\x06amount\x18\a \x01(\tR\x06amount\x12\x12\n" +
	"\x04data\x18\b \x01(\tR\x04data\x12\x19\n" +
	"\bchain_id\x18\t \x01(\tR\achainId\x12)\n" +
	"\x10contract_address\x18\n" +
	" \x01(\tR\x0fcontractAddress2\x8b\x03\n" +
	"\rSignerService\x12`\n" +
	"\x13exportPublicKeyList\x12\".signer.ExportPublicKeyListRequest\x1a#.signer.ExportPublicKeyListResponse\"\x00\x12]\n" +
	"\x12PublicKeyToAddress\x12!.signer.PublicKeyToAddressRequest\x1a\".signer.PublicKeyToAddressResponse\"\x00\x12N\n" +
	"\rsignTxMessage\x12\x1c.signer.SignTxMessageRequest\x1a\x1d.signer.SignTxMessageResponse\"\x00\x12i\n" +
	"\x16signTxMessageInBatches\x12%.signer.SignTxMessageInBatchesRequest\x1a&.signer.SignTxMessageInBatchesResponse\"\x00B1Z/wallet-sign-protobufs/generated/signer;signerpbb\x06proto3"

var (
	file_signer_proto_rawDescOnce sync.Once
	file_signer_proto_rawDescData []byte
)

func file_signer_proto_rawDescGZIP() []byte {
	file_signer_proto_rawDescOnce.Do(func() {
		file_signer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_signer_proto_rawDesc), len(file_signer_proto_rawDesc)))
	})
	return file_signer_proto_rawDescData
}

var file_signer_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_signer_proto_goTypes = []any{
	(*GetSupportSignWaysRequest)(nil),      // 0: signer.GetSupportSignWaysRequest
	(*GetSupportSignWaysResponse)(nil),     // 1: signer.GetSupportSignWaysResponse
	(*ExportPublicKeyListRequest)(nil),     // 2: signer.ExportPublicKeyListRequest
	(*ExportPublicKeyListResponse)(nil),    // 3: signer.ExportPublicKeyListResponse
	(*PublicKey)(nil),                      // 4: signer.PublicKey
	(*SignTxMessageRequest)(nil),           // 5: signer.SignTxMessageRequest
	(*SignTxMessageResponse)(nil),          // 6: signer.SignTxMessageResponse
	(*SignTxMessageInBatchesRequest)(nil),  // 7: signer.SignTxMessageInBatchesRequest
	(*SignTxMessageInBatchesResponse)(nil), // 8: signer.SignTxMessageInBatchesResponse
	(*PublicKeyToAddressRequest)(nil),      // 9: signer.PublicKeyToAddressRequest
	(*PublicKeyToAddressResponse)(nil),     // 10: signer.PublicKeyToAddressResponse
	(*EthereumTxBody)(nil),                 // 11: signer.EthereumTxBody
	(*common.RequestHeader)(nil),           // 12: common.RequestHeader
	(*common.ResponseHeader)(nil),          // 13: common.ResponseHeader
}
var file_signer_proto_depIdxs = []int32{
	12, // 0: signer.GetSupportSignWaysRequest.header:type_name -> common.RequestHeader
	13, // 1: signer.GetSupportSignWaysResponse.header:type_name -> common.ResponseHeader
	12, // 2: signer.ExportPublicKeyListRequest.header:type_name -> common.RequestHeader
	13, // 3: signer.ExportPublicKeyListResponse.header:type_name -> common.ResponseHeader
	4,  // 4: signer.ExportPublicKeyListResponse.list:type_name -> signer.PublicKey
	12, // 5: signer.SignTxMessageRequest.header:type_name -> common.RequestHeader
	13, // 6: signer.SignTxMessageResponse.header:type_name -> common.ResponseHeader
	12, // 7: signer.SignTxMessageInBatchesRequest.header:type_name -> common.RequestHeader
	5,  // 8: signer.SignTxMessageInBatchesRequest.list:type_name -> signer.SignTxMessageRequest
	13, // 9: signer.SignTxMessageInBatchesResponse.header:type_name -> common.ResponseHeader
	6,  // 10: signer.SignTxMessageInBatchesResponse.signed_tx_messages:type_name -> signer.SignTxMessageResponse
	12, // 11: signer.PublicKeyToAddressRequest.header:type_name -> common.RequestHeader
	13, // 12: signer.PublicKeyToAddressResponse.header:type_name -> common.ResponseHeader
	2,  // 13: signer.SignerService.exportPublicKeyList:input_type -> signer.ExportPublicKeyListRequest
	9,  // 14: signer.SignerService.PublicKeyToAddress:input_type -> signer.PublicKeyToAddressRequest
	5,  // 15: signer.SignerService.signTxMessage:input_type -> signer.SignTxMessageRequest
	7,  // 16: signer.SignerService.signTxMessageInBatches:input_type -> signer.SignTxMessageInBatchesRequest
	3,  // 17: signer.SignerService.exportPublicKeyList:output_type -> signer.ExportPublicKeyListResponse
	10, // 18: signer.SignerService.PublicKeyToAddress:output_type -> signer.PublicKeyToAddressResponse
	6,  // 19: signer.SignerService.signTxMessage:output_type -> signer.SignTxMessageResponse
	8,  // 20: signer.SignerService.signTxMessageInBatches:output_type -> signer.SignTxMessageInBatchesResponse
	17, // [17:21] is the sub-list for method output_type
	13, // [13:17] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_signer_proto_init() }
func file_signer_proto_init() {
	if File_signer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_signer_proto_rawDesc), len(file_signer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_signer_proto_goTypes,
		DependencyIndexes: file_signer_proto_depIdxs,
		MessageInfos:      file_signer_proto_msgTypes,
	}.Build()
	File_signer_proto = out.File
	file_signer_proto_goTypes = nil
	file_signer_proto_depIdxs = nil
}
