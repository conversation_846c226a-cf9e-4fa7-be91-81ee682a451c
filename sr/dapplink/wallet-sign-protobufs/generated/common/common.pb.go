// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: common.proto

package commonpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReturnCode int32

const (
	ReturnCode_RETURN_CODE_UNSPECIFIED ReturnCode = 0
	ReturnCode_ERROR                   ReturnCode = 1
	ReturnCode_SUCCESS                 ReturnCode = 2
)

// Enum value maps for ReturnCode.
var (
	ReturnCode_name = map[int32]string{
		0: "RETURN_CODE_UNSPECIFIED",
		1: "ERROR",
		2: "SUCCESS",
	}
	ReturnCode_value = map[string]int32{
		"RETURN_CODE_UNSPECIFIED": 0,
		"ERROR":                   1,
		"SUCCESS":                 2,
	}
)

func (x ReturnCode) Enum() *ReturnCode {
	p := new(ReturnCode)
	*p = x
	return p
}

func (x ReturnCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReturnCode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[0].Descriptor()
}

func (ReturnCode) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[0]
}

func (x ReturnCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReturnCode.Descriptor instead.
func (ReturnCode) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

// 所有请求通用的参数
type RequestHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	BearerToken   string                 `protobuf:"bytes,2,opt,name=bearer_token,json=bearerToken,proto3" json:"bearer_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestHeader) Reset() {
	*x = RequestHeader{}
	mi := &file_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestHeader) ProtoMessage() {}

func (x *RequestHeader) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestHeader.ProtoReflect.Descriptor instead.
func (*RequestHeader) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *RequestHeader) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *RequestHeader) GetBearerToken() string {
	if x != nil {
		return x.BearerToken
	}
	return ""
}

// 所有响应通用的参数
type ResponseHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          ReturnCode             `protobuf:"varint,1,opt,name=code,proto3,enum=common.ReturnCode" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseHeader) Reset() {
	*x = ResponseHeader{}
	mi := &file_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseHeader) ProtoMessage() {}

func (x *ResponseHeader) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseHeader.ProtoReflect.Descriptor instead.
func (*ResponseHeader) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *ResponseHeader) GetCode() ReturnCode {
	if x != nil {
		return x.Code
	}
	return ReturnCode_RETURN_CODE_UNSPECIFIED
}

func (x *ResponseHeader) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_common_proto protoreflect.FileDescriptor

const file_common_proto_rawDesc = "" +
	"\n" +
	"\fcommon.proto\x12\x06common\"H\n" +
	"\rRequestHeader\x12\x14\n" +
	"\x05chain\x18\x01 \x01(\tR\x05chain\x12!\n" +
	"\fbearer_token\x18\x02 \x01(\tR\vbearerToken\"R\n" +
	"\x0eResponseHeader\x12&\n" +
	"\x04code\x18\x01 \x01(\x0e2\x12.common.ReturnCodeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage*A\n" +
	"\n" +
	"ReturnCode\x12\x1b\n" +
	"\x17RETURN_CODE_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05ERROR\x10\x01\x12\v\n" +
	"\aSUCCESS\x10\x02B1Z/wallet-sign-protobufs/generated/common;commonpbb\x06proto3"

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData []byte
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)))
	})
	return file_common_proto_rawDescData
}

var file_common_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_common_proto_goTypes = []any{
	(ReturnCode)(0),        // 0: common.ReturnCode
	(*RequestHeader)(nil),  // 1: common.RequestHeader
	(*ResponseHeader)(nil), // 2: common.ResponseHeader
}
var file_common_proto_depIdxs = []int32{
	0, // 0: common.ResponseHeader.code:type_name -> common.ReturnCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		EnumInfos:         file_common_proto_enumTypes,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
