package ethereum

import (
	"context"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/log"
	"math/big"
	commonpb "wallet-sign-protobufs/generated/common"
	signerpb "wallet-sign-protobufs/generated/signer"
	"wallet-sign-signer/database"
	"wallet-sign-signer/ssm"
)

type Ethereum struct {
	signerpb.UnimplementedSignerServiceServer
	db *database.Database
}

func New(db *database.Database) signerpb.SignerServiceServer {
	return &Ethereum{
		db: db,
	}
}

func GetName() string {
	return "ethereum"
}

func (e *Ethereum) ExportPublicKeyList(ctx context.Context, req *signerpb.ExportPublicKeyListRequest) (*signerpb.ExportPublicKeyListResponse, error) {
	var retKeypairList = make([]*signerpb.PublicKey, 0, req.Count)

	var keypairList = make([]*ssm.Keypair, 0, req.Count)
	s := ssm.Factory(ssm.ECDSA)
	for i := 0; uint64(i) < req.Count; i++ {
		keypair, err := s.GenerateKeyPair()
		if err != nil {
			log.Error("An error occurred while generating keypair", "err", err)
			return &signerpb.ExportPublicKeyListResponse{
				Header: &commonpb.ResponseHeader{
					Code:    commonpb.ReturnCode_ERROR,
					Message: err.Error(),
				},
				List: nil,
			}, err
		}

		keypairList = append(keypairList, keypair)
		retKeypairList = append(retKeypairList, &signerpb.PublicKey{
			PublicKey:           keypair.PublicKey,
			CompressedPublicKey: keypair.CompressedPublicKey,
		})
	}

	for _, keypair := range keypairList {
		err := e.db.Store(keypair.PublicKey, keypair.PrivateKey)
		if err != nil {
			return nil, err
		}
	}

	return &signerpb.ExportPublicKeyListResponse{
		Header: &commonpb.ResponseHeader{
			Code:    commonpb.ReturnCode_SUCCESS,
			Message: "",
		},
		List: retKeypairList,
	}, nil
}
func (e *Ethereum) PublicKeyToAddress(ctx context.Context, req *signerpb.PublicKeyToAddressRequest) (*signerpb.PublicKeyToAddressResponse, error) {
	// 解码十六进制格式的公钥
	publicKeyBytes, err := hex.DecodeString(req.PublicKey)
	if err != nil {
		log.Error("decode public key failed:", err)
		return &signerpb.PublicKeyToAddressResponse{
			Header: &commonpb.ResponseHeader{
				Code:    commonpb.ReturnCode_ERROR,
				Message: "decode public key failed",
			},
			Address: "",
		}, nil
	}

	// 使用以太坊地址生成算法：Keccak256哈希的后20字节
	// 注意：跳过公钥的第一个字节（压缩标识符）
	address := common.BytesToAddress(crypto.Keccak256(publicKeyBytes[1:])[12:])
	return &signerpb.PublicKeyToAddressResponse{
		Header: &commonpb.ResponseHeader{
			Code:    commonpb.ReturnCode_SUCCESS,
			Message: "decode public key success",
		},
		Address: address.String(),
	}, nil
}

func (e *Ethereum) SignTxMessage(context context.Context, req *signerpb.SignTxMessageRequest) (*signerpb.SignTxMessageResponse, error) {
	res := &signerpb.SignTxMessageResponse{
		Header: &commonpb.ResponseHeader{
			Code: commonpb.ReturnCode_ERROR,
		},
	}

	decodeString, err := base64.StdEncoding.DecodeString(req.TxBodyBase64)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	var txBody *signerpb.EthereumTxBody
	err = json.Unmarshal(decodeString, &txBody)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	dFeeTx, err := buildDynamicFeeTx(txBody)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	txMessageHash, err := createEip1559UnSignTx(dFeeTx, dFeeTx.ChainID)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	privKey, err := e.db.Get(req.PublicKey)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	signature, err := ssm.Factory(ssm.ECDSA).Sign(privKey, txMessageHash)
	// 0x3a98c982a041f0519063a3a7d44d2ce56c17b8c6d7e895ca59ad4f7efa89c76e2025778bfde646ee27362f9194aa66fc470a637c068abf9dd00fc38c129ed23c01
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	inputSignatureByteList, err := hexutil.Decode(signature)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}
	
	eip1559Signer, signedTx, signedMessage, txHash, err := createEip1559SignedTx(dFeeTx, inputSignatureByteList, dFeeTx.ChainID)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}
	log.Info("sign transaction success",
		"eip1559Signer", eip1559Signer,
		"signedTx", signedTx,
		"signedMessage", signedMessage,
		"txHash", txHash,
	)
	res.Header.Code = commonpb.ReturnCode_SUCCESS
	res.SignedMessage = signedMessage
	res.TxHash = txHash
	res.TxMessageHash = txMessageHash
	return res, nil
}
func (e *Ethereum) SignTxMessageInBatches(context context.Context, req *signerpb.SignTxMessageInBatchesRequest) (*signerpb.SignTxMessageInBatchesResponse, error) {
	resList := make([]*signerpb.SignTxMessageResponse, len(req.List))
	retRes := &signerpb.SignTxMessageInBatchesResponse{
		Header: &commonpb.ResponseHeader{
			Code:    commonpb.ReturnCode_SUCCESS,
			Message: "",
		},
		SignedTxMessages: nil,
	}

	hasFailed := false
	for i, x := range req.List {
		res, _ := e.SignTxMessage(context, x)
		resList[i] = res
		hasFailed = hasFailed || res.Header.Code == commonpb.ReturnCode_ERROR
	}

	if !hasFailed {
		retRes.Header.Code = commonpb.ReturnCode_SUCCESS
	}

	retRes.SignedTxMessages = resList

	return retRes, nil
}

func isEthTransfer(contractAddress string) bool {
	return contractAddress == "" ||
		contractAddress == "******************************************" ||
		contractAddress == "0x00"
}

func buildErc20Data(toAddress common.Address, amount *big.Int) []byte {
	var data []byte

	transferFnSignature := []byte("transfer(address,uint256)")
	hash := crypto.Keccak256Hash(transferFnSignature)
	methodId := hash[:4]
	dataAddress := common.LeftPadBytes(toAddress.Bytes(), 32)
	dataAmount := common.LeftPadBytes(amount.Bytes(), 32)

	data = append(data, methodId...)
	data = append(data, dataAddress...)
	data = append(data, dataAmount...)

	return data
}

/**
  {
     "public_key": "04849CE860818A1398BF2004E443F38A53F9B8BC78389EC16308F68C44E9F619B559AE88C840200D61F3D02334AA99E33C86C039F93C082F2B0A9F8AB5D4909AE2",
     "compressed_publicKey": "02849CE860818A1398BF2004E443F38A53F9B8BC78389EC16308F68C44E9F619B5"
   }

address ******************************************



*/
