// Package ethereum 实现了以太坊区块链的签名服务
// 提供密钥生成、地址转换、交易签名等核心功能
package ethereum

import (
	"context"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/log"
	"math/big"
	commonpb "wallet-sign-protobufs/generated/common"
	signerpb "wallet-sign-protobufs/generated/signer"
	"wallet-sign-signer/database"
	"wallet-sign-signer/ssm"
)

// Ethereum 结构体实现了以太坊区块链的签名服务
// 包含数据库连接用于存储和检索密钥对
type Ethereum struct {
	signerpb.UnimplementedSignerServiceServer                    // 嵌入未实现的gRPC服务器
	db                                        *database.Database // 数据库实例，用于密钥存储
}

// New 创建一个新的以太坊签名服务实例
// 参数:
//   - db: 数据库实例，用于存储密钥对
//
// 返回:
//   - signerpb.SignerServiceServer: 签名服务接口实现
func New(db *database.Database) signerpb.SignerServiceServer {
	return &Ethereum{
		db: db,
	}
}

// GetName 返回当前区块链的名称标识
// 返回:
//   - string: 区块链名称 "ethereum"
func GetName() string {
	return "ethereum"
}

// ExportPublicKeyList 批量生成指定数量的密钥对并导出公钥列表
// 该方法会生成新的ECDSA密钥对，将私钥存储到数据库，并返回公钥信息
// 参数:
//   - ctx: 上下文对象
//   - req: 导出公钥列表请求，包含需要生成的密钥对数量
//
// 返回:
//   - *signerpb.ExportPublicKeyListResponse: 包含生成的公钥列表的响应
//   - error: 错误信息
func (e *Ethereum) ExportPublicKeyList(ctx context.Context, req *signerpb.ExportPublicKeyListRequest) (*signerpb.ExportPublicKeyListResponse, error) {
	// 初始化返回的公钥列表，预分配容量以提高性能
	var retKeypairList = make([]*signerpb.PublicKey, 0, req.Count)

	// 初始化临时密钥对列表，用于后续批量存储
	var keypairList = make([]*ssm.Keypair, 0, req.Count)

	// 创建ECDSA密钥生成器
	s := ssm.Factory(ssm.ECDSA)

	// 循环生成指定数量的密钥对
	for i := 0; uint64(i) < req.Count; i++ {
		// 生成新的密钥对
		keypair, err := s.GenerateKeyPair()
		if err != nil {
			log.Error("An error occurred while generating keypair", "err", err)
			return &signerpb.ExportPublicKeyListResponse{
				Header: &commonpb.ResponseHeader{
					Code:    commonpb.ReturnCode_ERROR,
					Message: err.Error(),
				},
				List: nil,
			}, err
		}

		// 将密钥对添加到临时列表
		keypairList = append(keypairList, keypair)
		// 将公钥信息添加到返回列表
		retKeypairList = append(retKeypairList, &signerpb.PublicKey{
			PublicKey:           keypair.PublicKey,           // 完整公钥
			CompressedPublicKey: keypair.CompressedPublicKey, // 压缩公钥
		})
	}

	// 批量存储所有生成的密钥对到数据库
	for _, keypair := range keypairList {
		err := e.db.Store(keypair.PublicKey, keypair.PrivateKey)
		if err != nil {
			return nil, err
		}
	}

	// 返回成功响应和公钥列表
	return &signerpb.ExportPublicKeyListResponse{
		Header: &commonpb.ResponseHeader{
			Code:    commonpb.ReturnCode_SUCCESS,
			Message: "",
		},
		List: retKeypairList,
	}, nil
}

// PublicKeyToAddress 将公钥转换为以太坊地址
// 使用以太坊标准的地址生成算法：对公钥进行Keccak256哈希，取后20字节作为地址
// 参数:
//   - ctx: 上下文对象
//   - req: 公钥转地址请求，包含十六进制格式的公钥字符串
//
// 返回:
//   - *signerpb.PublicKeyToAddressResponse: 包含生成的以太坊地址的响应
//   - error: 错误信息
func (e *Ethereum) PublicKeyToAddress(ctx context.Context, req *signerpb.PublicKeyToAddressRequest) (*signerpb.PublicKeyToAddressResponse, error) {
	// 解码十六进制格式的公钥字符串为字节数组
	publicKeyBytes, err := hex.DecodeString(req.PublicKey)
	if err != nil {
		log.Error("decode public key failed:", err)
		return &signerpb.PublicKeyToAddressResponse{
			Header: &commonpb.ResponseHeader{
				Code:    commonpb.ReturnCode_ERROR,
				Message: "decode public key failed",
			},
			Address: "",
		}, nil
	}

	// 使用以太坊地址生成算法：
	// 1. 跳过公钥的第一个字节（0x04前缀，表示未压缩公钥）
	// 2. 对剩余64字节进行Keccak256哈希运算
	// 3. 取哈希结果的后20字节（从第12字节开始）作为地址
	address := common.BytesToAddress(crypto.Keccak256(publicKeyBytes[1:])[12:])

	return &signerpb.PublicKeyToAddressResponse{
		Header: &commonpb.ResponseHeader{
			Code:    commonpb.ReturnCode_SUCCESS,
			Message: "decode public key success",
		},
		Address: address.String(), // 返回0x前缀的十六进制地址格式
	}, nil
}

// SignTxMessage 对单个交易消息进行签名
// 支持EIP-1559类型的以太坊交易，包括ETH转账和ERC-20代币转账
// 参数:
//   - context: 上下文对象
//   - req: 交易签名请求，包含Base64编码的交易体和公钥
//
// 返回:
//   - *signerpb.SignTxMessageResponse: 包含签名结果的响应
//   - error: 错误信息
func (e *Ethereum) SignTxMessage(context context.Context, req *signerpb.SignTxMessageRequest) (*signerpb.SignTxMessageResponse, error) {
	// 初始化响应对象，默认为错误状态
	res := &signerpb.SignTxMessageResponse{
		Header: &commonpb.ResponseHeader{
			Code: commonpb.ReturnCode_ERROR,
		},
	}

	// 1. 解码Base64编码的交易体
	decodeString, err := base64.StdEncoding.DecodeString(req.TxBodyBase64)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 2. 反序列化交易体JSON数据
	var txBody *signerpb.EthereumTxBody
	err = json.Unmarshal(decodeString, &txBody)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 3. 构建EIP-1559动态费用交易对象
	dFeeTx, err := buildDynamicFeeTx(txBody)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 4. 创建未签名交易的哈希值（用于签名）
	txMessageHash, err := createEip1559UnSignTx(dFeeTx, dFeeTx.ChainID)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 5. 从数据库获取对应公钥的私钥
	privKey, err := e.db.Get(req.PublicKey)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 6. 使用ECDSA算法对交易哈希进行签名
	signature, err := ssm.Factory(ssm.ECDSA).Sign(privKey, txMessageHash)
	// 签名示例: 0x3a98c982a041f0519063a3a7d44d2ce56c17b8c6d7e895ca59ad4f7efa89c76e2025778bfde646ee27362f9194aa66fc470a637c068abf9dd00fc38c129ed23c01
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 7. 将十六进制签名字符串转换为字节数组
	inputSignatureByteList, err := hexutil.Decode(signature)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 8. 创建完整的已签名交易
	eip1559Signer, signedTx, signedMessage, txHash, err := createEip1559SignedTx(dFeeTx, inputSignatureByteList, dFeeTx.ChainID)
	if err != nil {
		res.Header.Message = err.Error()
		return res, nil
	}

	// 9. 记录签名成功的日志信息
	log.Info("sign transaction success",
		"eip1559Signer", eip1559Signer, // EIP-1559签名器
		"signedTx", signedTx, // 已签名交易对象
		"signedMessage", signedMessage, // 已签名交易的RLP编码
		"txHash", txHash, // 交易哈希
	)

	// 10. 设置成功响应并返回签名结果
	res.Header.Code = commonpb.ReturnCode_SUCCESS
	res.SignedMessage = signedMessage // 已签名交易的十六进制编码
	res.TxHash = txHash               // 交易哈希
	res.TxMessageHash = txMessageHash // 原始交易消息哈希
	return res, nil
}

// SignTxMessageInBatches 批量签名多个交易消息
// 对请求中的每个交易进行签名，并返回所有签名结果
// 如果任何一个交易签名失败，整体状态会标记为失败，但仍会返回所有结果
// 参数:
//   - context: 上下文对象
//   - req: 批量交易签名请求，包含多个交易签名请求
//
// 返回:
//   - *signerpb.SignTxMessageInBatchesResponse: 包含所有交易签名结果的响应
//   - error: 错误信息
func (e *Ethereum) SignTxMessageInBatches(context context.Context, req *signerpb.SignTxMessageInBatchesRequest) (*signerpb.SignTxMessageInBatchesResponse, error) {
	// 初始化结果列表，长度与请求列表相同
	resList := make([]*signerpb.SignTxMessageResponse, len(req.List))

	// 初始化批量响应对象，默认为失败状态
	retRes := &signerpb.SignTxMessageInBatchesResponse{
		Header: &commonpb.ResponseHeader{
			Code:    commonpb.ReturnCode_ERROR,
			Message: "",
		},
		SignedTxMessages: nil,
	}

	// 标记是否有任何交易签名失败
	hasFailed := false

	// 遍历所有交易请求，逐个进行签名
	for i, x := range req.List {
		// 调用单个交易签名方法，忽略错误（错误信息在响应中）
		res, _ := e.SignTxMessage(context, x)
		resList[i] = res
		// 检查当前交易是否签名失败
		hasFailed = hasFailed || res.Header.Code == commonpb.ReturnCode_ERROR
	}

	// 如果没有任何交易失败，改为成功状态
	if !hasFailed {
		retRes.Header.Code = commonpb.ReturnCode_SUCCESS
	}

	// 设置签名结果列表
	retRes.SignedTxMessages = resList

	return retRes, nil
}

// isEthTransfer 判断是否为原生ETH转账交易
// 通过检查合约地址是否为空或零地址来判断
// 参数:
//   - contractAddress: 合约地址字符串
//
// 返回:
//   - bool: true表示ETH转账，false表示合约调用
func isEthTransfer(contractAddress string) bool {
	return contractAddress == "" ||
		contractAddress == "******************************************" ||
		contractAddress == "0x00"
}

// buildErc20Data 构建ERC-20代币转账的交易数据
// 按照ERC-20标准的transfer函数格式构建调用数据
// 参数:
//   - toAddress: 接收方地址
//   - amount: 转账金额（以代币最小单位计算）
//
// 返回:
//   - []byte: 编码后的交易数据
func buildErc20Data(toAddress common.Address, amount *big.Int) []byte {
	var data []byte

	// 1. 构建transfer函数签名并计算方法ID
	transferFnSignature := []byte("transfer(address,uint256)")
	hash := crypto.Keccak256Hash(transferFnSignature)
	methodId := hash[:4] // 取前4字节作为方法ID

	// 2. 编码接收方地址（左填充到32字节）
	dataAddress := common.LeftPadBytes(toAddress.Bytes(), 32)

	// 3. 编码转账金额（左填充到32字节）
	dataAmount := common.LeftPadBytes(amount.Bytes(), 32)

	// 4. 按顺序拼接所有数据：方法ID + 地址 + 金额
	data = append(data, methodId...)
	data = append(data, dataAddress...)
	data = append(data, dataAmount...)

	return data
}

/*
示例密钥对和地址信息：

密钥对示例:
{
  "public_key": "04849CE860818A1398BF2004E443F38A53F9B8BC78389EC16308F68C44E9F619B559AE88C840200D61F3D02334AA99E33C86C039F93C082F2B0A9F8AB5D4909AE2",
  "compressed_publicKey": "02849CE860818A1398BF2004E443F38A53F9B8BC78389EC16308F68C44E9F619B5"
}

对应的以太坊地址: ******************************************

说明:
- public_key: 完整的未压缩公钥（65字节，以04开头）
- compressed_publicKey: 压缩公钥（33字节，以02或03开头）
- 地址通过对完整公钥（去除04前缀）进行Keccak256哈希，取后20字节生成
*/
