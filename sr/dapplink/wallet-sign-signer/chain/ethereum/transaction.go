// Package ethereum 的交易处理模块
// 实现了EIP-1559类型的以太坊交易构建、签名和序列化功能
package ethereum

import (
	"encoding/hex"
	"errors"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/log"
	"github.com/ethereum/go-ethereum/rlp"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"math/big"
	signerpb "wallet-sign-protobufs/generated/signer"
)

// buildDynamicFeeTx 构建EIP-1559动态费用交易对象
// 将protobuf格式的交易体转换为以太坊的DynamicFeeTx类型
// 支持ETH转账和ERC-20代币转账两种类型
// 参数:
//   - txBody: protobuf格式的以太坊交易体
// 返回:
//   - *types.DynamicFeeTx: 构建好的动态费用交易对象
//   - error: 错误信息
func buildDynamicFeeTx(txBody *signerpb.EthereumTxBody) (*types.DynamicFeeTx, error) {
	// 1. 初始化大整数对象用于存储各种费用和金额
	chainID := new(big.Int)
	maxPriorityFeePerGas := new(big.Int)
	maxFeePerGas := new(big.Int)
	amount := new(big.Int)

	// 2. 解析和验证各个字符串格式的数值参数
	// 解析链ID
	if _, ok := chainID.SetString(txBody.ChainId, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid chain id: %s", txBody.ChainId)
	}
	// 解析最大优先费用（矿工小费）
	if _, ok := maxPriorityFeePerGas.SetString(txBody.MaxPriorityFeePerGas, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid max priority fee per gas: %s", txBody.MaxPriorityFeePerGas)
	}
	// 解析最大费用上限
	if _, ok := maxFeePerGas.SetString(txBody.MaxFeePerGas, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid max fee per gas: %s", txBody.MaxFeePerGas)
	}
	// 解析转账金额
	if _, ok := amount.SetString(txBody.Amount, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid amount: %s", txBody.Amount)
	}

	// 3. 处理接收地址
	toAddress := common.HexToAddress(txBody.To)
	var finalToAddress common.Address // 最终的交易接收地址
	var finalAmount *big.Int          // 最终的转账金额
	var buildData []byte              // 交易数据（用于合约调用）

	// 记录合约地址检查日志
	log.Info("contract address check",
		"contractAddress", txBody.ContractAddress,
		"isEthTransfer", isEthTransfer(txBody.ContractAddress),
	)

	// 4. 根据交易类型设置不同的参数
	if isEthTransfer(txBody.ContractAddress) {
		// ETH原生转账：直接转账到目标地址
		finalToAddress = toAddress
		finalAmount = amount
	} else {
		// ERC-20代币转账：调用合约的transfer方法
		contractAddress := common.HexToAddress(txBody.ContractAddress)
		buildData = buildErc20Data(toAddress, amount) // 构建transfer调用数据
		finalToAddress = contractAddress              // 交易发送到合约地址
		finalAmount = big.NewInt(0)                   // ETH金额为0
	}

	// 5. 创建并返回动态费用交易对象
	return &types.DynamicFeeTx{
		ChainID:   chainID,               // 链ID
		Nonce:     txBody.Nonce,          // 交易序号
		GasTipCap: maxPriorityFeePerGas,  // 最大优先费用（矿工小费）
		GasFeeCap: maxFeePerGas,          // 最大费用上限
		Gas:       txBody.GasLimit,       // Gas限制
		To:        &finalToAddress,       // 接收地址
		Value:     finalAmount,           // 转账金额
		Data:      buildData,             // 交易数据
	}, nil
}

// createEip1559UnSignTx 创建EIP-1559未签名交易的哈希值
// 该哈希值将用于后续的数字签名过程
// 参数:
//   - txData: 动态费用交易数据
//   - chainId: 区块链ID
// 返回:
//   - string: 交易哈希的十六进制字符串表示
//   - error: 错误信息
func createEip1559UnSignTx(txData *types.DynamicFeeTx, chainId *big.Int) (string, error) {
	// 1. 创建交易对象
	tx := types.NewTx(txData)

	// 2. 创建对应链ID的签名器
	signer := types.LatestSignerForChainID(chainId)

	// 3. 计算交易哈希（用于签名的消息哈希）
	txHash := signer.Hash(tx)

	return txHash.String(), nil
}

// createEip1559SignedTx 创建完整的EIP-1559已签名交易
// 将签名应用到交易上，并生成可广播的交易数据
// 参数:
//   - txData: 动态费用交易数据
//   - signature: 数字签名字节数组
//   - chainId: 区块链ID
// 返回:
//   - types.Signer: 签名器对象
//   - *types.Transaction: 已签名的交易对象
//   - string: RLP编码的已签名交易十六进制字符串（可用于广播）
//   - string: 最终交易哈希
//   - error: 错误信息
func createEip1559SignedTx(txData *types.DynamicFeeTx, signature []byte, chainId *big.Int) (types.Signer, *types.Transaction, string, string, error) {
	// 1. 创建交易对象
	tx := types.NewTx(txData)

	// 2. 创建对应链ID的签名器
	signer := types.LatestSignerForChainID(chainId)

	// 3. 将签名应用到交易上，生成已签名交易
	signedTx, err := tx.WithSignature(signer, signature)
	if err != nil {
		return nil, nil, "", "", errors.New("tx with signature fail")
	}

	// 4. 将已签名交易进行RLP编码，生成可广播的字节数据
	signedTxData, err := rlp.EncodeToBytes(signedTx)
	if err != nil {
		return nil, nil, "", "", errors.New("encode tx to byte fail")
	}

	// 5. 返回所有相关信息
	// 注意：这里跳过前4字节可能是为了去除某种特定的前缀
	return signer, signedTx, "0x" + hex.EncodeToString(signedTxData)[4:], signedTx.Hash().String(), nil
}
