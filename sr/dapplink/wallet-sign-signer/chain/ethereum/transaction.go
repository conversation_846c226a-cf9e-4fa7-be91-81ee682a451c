package ethereum

import (
	"encoding/hex"
	"errors"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/log"
	"github.com/ethereum/go-ethereum/rlp"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"math/big"
	signerpb "wallet-sign-protobufs/generated/signer"
)

func buildDynamicFeeTx(txBody *signerpb.EthereumTxBody) (*types.DynamicFeeTx, error) {
	chainID := new(big.Int)
	maxPriorityFeePerGas := new(big.Int)
	maxFeePerGas := new(big.Int)
	amount := new(big.Int)

	if _, ok := chainID.SetString(txBody.ChainId, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid chain id: %s", txBody.ChainId)
	}
	if _, ok := maxPriorityFeePerGas.SetString(txBody.MaxPriorityFeePerGas, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid max priority fee per gas: %s", txBody.MaxPriorityFeePerGas)
	}
	if _, ok := maxFeePerGas.SetString(txBody.MaxFeePerGas, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid max fee per gas: %s", txBody.MaxFeePerGas)
	}
	if _, ok := amount.SetString(txBody.Amount, 10); !ok {
		return nil, status.Errorf(codes.InvalidArgument, "invalid amount: %s", txBody.Amount)
	}

	// 4. Handle addresses and data
	toAddress := common.HexToAddress(txBody.To)
	var finalToAddress common.Address
	var finalAmount *big.Int
	var buildData []byte
	log.Info("contract address check",
		"contractAddress", txBody.ContractAddress,
		"isEthTransfer", isEthTransfer(txBody.ContractAddress),
	)

	// 5. Handle contract interaction vs direct transfer
	if isEthTransfer(txBody.ContractAddress) {
		finalToAddress = toAddress
		finalAmount = amount
	} else {
		contractAddress := common.HexToAddress(txBody.ContractAddress)
		buildData = buildErc20Data(toAddress, amount)
		finalToAddress = contractAddress
		finalAmount = big.NewInt(0)
	}

	// 6. Create dynamic fee transaction

	return &types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     txBody.Nonce,
		GasTipCap: maxPriorityFeePerGas,
		GasFeeCap: maxFeePerGas,
		Gas:       txBody.GasLimit,
		To:        &finalToAddress,
		Value:     finalAmount,
		Data:      buildData,
	}, nil
}

func createEip1559UnSignTx(txData *types.DynamicFeeTx, chainId *big.Int) (string, error) {
	tx := types.NewTx(txData)
	// 签名者
	signer := types.LatestSignerForChainID(chainId)
	txHash := signer.Hash(tx)
	return txHash.String(), nil
}

func createEip1559SignedTx(txData *types.DynamicFeeTx, signature []byte, chainId *big.Int) (types.Signer, *types.Transaction, string, string, error) {
	tx := types.NewTx(txData)
	signer := types.LatestSignerForChainID(chainId)
	signedTx, err := tx.WithSignature(signer, signature)
	if err != nil {
		return nil, nil, "", "", errors.New("tx with signature fail")
	}
	signedTxData, err := rlp.EncodeToBytes(signedTx)
	if err != nil {
		return nil, nil, "", "", errors.New("encode tx to byte fail")
	}
	return signer, signedTx, "0x" + hex.EncodeToString(signedTxData)[4:], signedTx.Hash().String(), nil
}
