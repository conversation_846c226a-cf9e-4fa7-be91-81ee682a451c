package chain

import (
	"context"
	"errors"
	"fmt"
	"sync"
	commonpb "wallet-sign-protobufs/generated/common"
	signerpb "wallet-sign-protobufs/generated/signer"
	"wallet-sign-signer/chain/ethereum"
	"wallet-sign-signer/config"
	"wallet-sign-signer/database"
)

type Dispatcher struct {
	config *config.Config
	signerpb.UnimplementedSignerServiceServer
	db *database.Database
}

func NewDispatcher(config *config.Config, db *database.Database) (*Dispatcher, error) {
	return &Dispatcher{
		config: config,
		db:     db,
	}, nil
}

var chainFactoryMap = map[string]func(db *database.Database) signerpb.SignerServiceServer{
	ethereum.GetName(): ethereum.New,
}

var chainMap = sync.Map{} //map[string]signerpb.SignerServiceServer

func (d *Dispatcher) factory(name string) (signerpb.SignerServiceServer, error) {
	if value, ok := chainMap.Load(name); ok {
		return value.(signerpb.SignerServiceServer), nil
	}

	if factory, ok := chainFactoryMap[name]; ok {
		server := factory(d.db)
		chainMap.Store(name, server)
		return server, nil
	}

	return nil, errors.New(fmt.Sprintf("chain %s is not implemented", name))
}

type CommonReq interface {
	GetHeader() *commonpb.RequestHeader
}

type CommonRes struct {
	Header *commonpb.ResponseHeader
}

func (d *Dispatcher) preHandler(ctx context.Context, req CommonReq) (signerpb.SignerServiceServer, *CommonRes) {
	chain, err := d.factory(req.GetHeader().Chain)
	if err != nil {
		return nil, &CommonRes{
			Header: &commonpb.ResponseHeader{
				Code:    commonpb.ReturnCode_ERROR,
				Message: err.Error(),
			},
		}
	}

	return chain, nil
}

func (d *Dispatcher) ExportPublicKeyList(ctx context.Context, req *signerpb.ExportPublicKeyListRequest) (*signerpb.ExportPublicKeyListResponse, error) {
	chain, res := d.preHandler(ctx, req)
	if res != nil {
		return &signerpb.ExportPublicKeyListResponse{
			Header: &commonpb.ResponseHeader{
				Code:    res.Header.Code,
				Message: res.Header.Message,
			},
		}, nil
	}

	if req.Count > d.config.MaxExportKeyPairCountPerRequest {
		return &signerpb.ExportPublicKeyListResponse{
			Header: &commonpb.ResponseHeader{
				Code:    commonpb.ReturnCode_ERROR,
				Message: "count is too large",
			},
		}, nil
	}

	return chain.ExportPublicKeyList(ctx, req)
}
func (d *Dispatcher) PublicKeyToAddress(ctx context.Context, req *signerpb.PublicKeyToAddressRequest) (*signerpb.PublicKeyToAddressResponse, error) {
	chain, res := d.preHandler(ctx, req)
	if res != nil {
		return &signerpb.PublicKeyToAddressResponse{
			Header: &commonpb.ResponseHeader{
				Code:    res.Header.Code,
				Message: res.Header.Message,
			},
		}, nil
	}

	return chain.PublicKeyToAddress(ctx, req)
}
func (d *Dispatcher) SignTxMessage(context context.Context, req *signerpb.SignTxMessageRequest) (*signerpb.SignTxMessageResponse, error) {
	chain, res := d.preHandler(context, req)
	if res != nil {
		return &signerpb.SignTxMessageResponse{
			Header: &commonpb.ResponseHeader{
				Code:    res.Header.Code,
				Message: res.Header.Message,
			},
		}, nil
	}

	return chain.SignTxMessage(context, req)
}
func (d *Dispatcher) SignTxMessageInBatches(context context.Context, req *signerpb.SignTxMessageInBatchesRequest) (*signerpb.SignTxMessageInBatchesResponse, error) {
	chain, res := d.preHandler(context, req)
	if res != nil {
		return &signerpb.SignTxMessageInBatchesResponse{
			Header: &commonpb.ResponseHeader{
				Code:    res.Header.Code,
				Message: res.Header.Message,
			},
		}, nil
	}

	return chain.SignTxMessageInBatches(context, req)
}
