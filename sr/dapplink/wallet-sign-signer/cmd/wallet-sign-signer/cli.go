package main

import (
	"github.com/urfave/cli/v2"
	"wallet-sign-common/flags"
	"wallet-sign-signer/config"
	"wallet-sign-signer/service"
)

func NewCli() *cli.App {
	return &cli.App{
		Name:    "wallet-sign-signer",
		Version: "0.0.1",
		Commands: []*cli.Command{
			{
				Name:   "rpc",
				Action: runRpc,
				Flags: []cli.Flag{
					flags.Config,
				},
			},
		},
	}
}

func runRpc(ctx *cli.Context) error {
	cfg, err := config.NewConfig(ctx.String(flags.Config.Name))
	if err != nil {
		return err
	}

	server, err := service.NewRpcServer(cfg)
	if err != nil {
		return err
	}

	return server.Start()
}
