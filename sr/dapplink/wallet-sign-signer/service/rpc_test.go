package service_test

import (
	"context"
	"encoding/base64"
	"github.com/goccy/go-json"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"log"
	"testing"
	commonpb "wallet-sign-protobufs/generated/common"
	signerpb "wallet-sign-protobufs/generated/signer"
)

/**



 */

var TestPublicKey = "04849CE860818A1398BF2004E443F38A53F9B8BC78389EC16308F68C44E9F619B559AE88C840200D61F3D02334AA99E33C86C039F93C082F2B0A9F8AB5D4909AE2"
var TestAddress = "******************************************"
var client signerpb.SignerServiceClient

func init() {
	creds, err := credentials.NewClientTLSFromFile("../certs/server.crt", "")
	if err != nil {
		log.Fatalf("Failed to generate credentials %v", err)
	}

	conn, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithTransportCredentials(creds),
	)
	if err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}

	client = signerpb.NewSignerServiceClient(conn)
}

type Header interface {
	GetHeader() *commonpb.ResponseHeader
}

func assertSuccess[T Header](t *testing.T, res T) {
	if res.GetHeader().Message != "" && res.GetHeader().Code == commonpb.ReturnCode_ERROR {
		t.Log("err", res.GetHeader().GetMessage())
	}
	assert.Equal(t, res.GetHeader().GetCode(), commonpb.ReturnCode_SUCCESS)
}

func TestRpcServer_ExportPublicKeyList(t *testing.T) {
	res, err := client.ExportPublicKeyList(context.Background(), &signerpb.ExportPublicKeyListRequest{
		Header: &commonpb.RequestHeader{
			Chain: "ethereum",
		},
		Count: 1,
	})

	assert.NoError(t, err)
	assertSuccess(t, res)
	assert.Equal(t, 1, len(res.List))
}

func TestRpcServer_PublicKeyToAddress(t *testing.T) {
	res, err := client.PublicKeyToAddress(context.Background(), &signerpb.PublicKeyToAddressRequest{
		Header: &commonpb.RequestHeader{
			Chain: "ethereum",
		},
		PublicKey: TestPublicKey,
	})

	assert.NoError(t, err)
	assertSuccess(t, res)
	assert.Equal(t, TestAddress, res.Address)
}

func TestRpcServer_SignTxMessage(t *testing.T) {
	txBody := &signerpb.EthereumTxBody{
		Nonce:                0,
		GasPrice:             "",
		GasLimit:             21000,
		MaxFeePerGas:         "327993150328",
		MaxPriorityFeePerGas: "32799315032",
		To:                   "******************************************",
		Amount:               "80000000000000000", // 0.08 ETH
		Data:                 "",
		ChainId:              "11155111",
		ContractAddress:      "0x00",
	}

	txBodyJson, _ := json.Marshal(txBody)

	res, err := client.SignTxMessage(context.Background(), &signerpb.SignTxMessageRequest{
		Header: &commonpb.RequestHeader{
			Chain: "ethereum",
		},
		PublicKey:    TestPublicKey,
		TxBodyBase64: base64.StdEncoding.EncodeToString(txBodyJson),
	})

	assert.NoError(t, err)

	log.Println("res", res)
	log.Println("SignedMessage", res.SignedMessage)
	log.Println("TxHash", res.TxHash)
	log.Println("TxMessageHash", res.TxMessageHash)

	assertSuccess(t, res)
	assert.Greater(t, len(res.SignedMessage), 1)
	assert.Greater(t, len(res.TxMessageHash), 1)
	assert.Greater(t, len(res.TxHash), 1)
}
