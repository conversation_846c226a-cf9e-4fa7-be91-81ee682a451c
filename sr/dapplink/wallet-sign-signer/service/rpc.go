package service

import (
	"google.golang.org/grpc"
	"wallet-sign-common/rpc"
	signerpb "wallet-sign-protobufs/generated/signer"
	"wallet-sign-signer/chain"
	"wallet-sign-signer/config"
	"wallet-sign-signer/database"
)

type RpcServer struct {
	config     *config.Config
	db         *database.Database
	dispatcher *chain.Dispatcher
}

func (r *RpcServer) Start() error {
	return rpc.StartServer(r.config.Rpc, func(s *grpc.Server) error {
		signerpb.RegisterSignerServiceServer(s, r.dispatcher)
		return nil
	})
}

func NewRpcServer(config *config.Config) (*RpcServer, error) {
	db, err := database.NewDB(config)
	if err != nil {
		return nil, err
	}

	dispatcher, err := chain.NewDispatcher(config, db)
	if err != nil {
		return nil, err
	}

	return &RpcServer{
		config:     config,
		db:         db,
		dispatcher: dispatcher,
	}, nil
}
