package database

import (
	"errors"
	"github.com/syndtr/goleveldb/leveldb"
	"wallet-sign-signer/config"
)

var key = "sr"

type Database struct {
	db  *leveldb.DB
	aes *AESCrypto
}

func (d *Database) Store(k string, v string) error {
	encrypted, err := d.aes.Encrypt(v)
	if err != nil {
		return err
	}

	return d.db.Put(
		[]byte(k),
		[]byte(encrypted),
		nil,
	)
}

func (d *Database) Get(k string) (string, error) {
	v, err := d.db.Get([]byte(k), nil)

	if err != nil {
		return "", errors.New("get value from database failed")
	}

	decrypt, err := d.aes.Decrypt(string(v))
	if err != nil {
		return "", errors.New("decrypt value from database failed")
	}

	return decrypt, nil
}

func NewDB(config *config.Config) (*Database, error) {
	db, err := leveldb.OpenFile(config.DB.Location, nil)
	if err != nil {
		return nil, err
	}

	aes, err := NewAESCrypto(config.AesKey)
	if err != nil {
		return nil, err
	}
	return &Database{
		db:  db,
		aes: aes,
	}, nil
}
