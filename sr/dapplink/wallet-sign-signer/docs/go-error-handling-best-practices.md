# Go 语言异常处理最佳实践

## 目录
1. [错误处理模式](#错误处理模式)
2. [错误创建和传播](#错误创建和传播)
3. [具体代码示例](#具体代码示例)
4. [项目改进建议](#项目改进建议)
5. [工具和库推荐](#工具和库推荐)
6. [性能考虑](#性能考虑)

## 错误处理模式

### 1. 正确使用 `error` 接口

Go 语言的错误处理基于 `error` 接口：

```go
type error interface {
    Error() string
}
```

**最佳实践：**

```go
// ✅ 好的做法：明确的错误检查
func processData(data []byte) error {
    if len(data) == 0 {
        return fmt.Errorf("数据不能为空")
    }
    
    result, err := parseData(data)
    if err != nil {
        return fmt.Errorf("解析数据失败: %w", err)
    }
    
    return nil
}

// ❌ 避免的做法：忽略错误
func badExample() {
    data, _ := readFile("config.json") // 忽略错误
    processData(data)
}
```

### 2. `panic` 和 `recover` 的使用场景

**何时使用 `panic`：**
- 程序遇到无法恢复的错误
- 初始化阶段的致命错误
- 开发阶段的断言失败

```go
// ✅ 合适的 panic 使用
func init() {
    if os.Getenv("REQUIRED_ENV") == "" {
        panic("REQUIRED_ENV 环境变量未设置")
    }
}

// ✅ 使用 recover 优雅处理 panic
func safeExecute(fn func()) (err error) {
    defer func() {
        if r := recover(); r != nil {
            err = fmt.Errorf("执行失败: %v", r)
        }
    }()
    
    fn()
    return nil
}
```

### 3. 错误包装和展开

**使用 `fmt.Errorf` 和 `%w` 动词：**

```go
// ✅ 错误包装
func connectDatabase() error {
    conn, err := sql.Open("postgres", dsn)
    if err != nil {
        return fmt.Errorf("连接数据库失败: %w", err)
    }
    return nil
}

// ✅ 错误展开和检查
func handleDatabaseError(err error) {
    var netErr *net.OpError
    if errors.As(err, &netErr) {
        log.Printf("网络错误: %v", netErr)
    }
    
    if errors.Is(err, sql.ErrNoRows) {
        log.Printf("未找到记录")
    }
}
```

## 错误创建和传播

### 1. 创建有意义的错误信息

```go
// ✅ 好的错误信息：包含上下文
func validatePrivateKey(key string) error {
    if key == "" {
        return fmt.Errorf("私钥不能为空")
    }
    
    if len(key) != 64 {
        return fmt.Errorf("私钥长度无效: 期望64字符，实际%d字符", len(key))
    }
    
    if !isHexString(key) {
        return fmt.Errorf("私钥格式无效: 必须是十六进制字符串")
    }
    
    return nil
}
```

### 2. 自定义错误类型

```go
// 定义错误类型
type CryptoError struct {
    Op      string // 操作名称
    KeyType string // 密钥类型
    Err     error  // 底层错误
}

func (e *CryptoError) Error() string {
    return fmt.Sprintf("加密操作失败 [%s:%s]: %v", e.Op, e.KeyType, e.Err)
}

func (e *CryptoError) Unwrap() error {
    return e.Err
}

// 使用自定义错误
func signMessage(keyType, privateKey, message string) (string, error) {
    if err := validatePrivateKey(privateKey); err != nil {
        return "", &CryptoError{
            Op:      "sign",
            KeyType: keyType,
            Err:     err,
        }
    }
    
    // 签名逻辑...
    return signature, nil
}
```

### 3. 错误常量和变量

```go
// 定义错误常量
var (
    ErrInvalidPrivateKey = errors.New("无效的私钥")
    ErrInvalidPublicKey  = errors.New("无效的公钥")
    ErrSignatureFailed   = errors.New("签名失败")
    ErrVerifyFailed      = errors.New("验证失败")
)

// 使用错误常量
func verifySignature(pubKey, message, signature string) error {
    if !isValidPublicKey(pubKey) {
        return fmt.Errorf("%w: %s", ErrInvalidPublicKey, pubKey)
    }
    
    if !verify(pubKey, message, signature) {
        return ErrVerifyFailed
    }
    
    return nil
}
```

## 具体代码示例

### 1. 函数中的错误处理模式

```go
// ✅ 标准的错误处理模式
func processTransaction(tx *Transaction) (*Result, error) {
    // 1. 参数验证
    if tx == nil {
        return nil, fmt.Errorf("交易不能为空")
    }
    
    // 2. 业务逻辑处理
    validated, err := validateTransaction(tx)
    if err != nil {
        return nil, fmt.Errorf("交易验证失败: %w", err)
    }
    
    // 3. 执行操作
    result, err := executeTransaction(validated)
    if err != nil {
        return nil, fmt.Errorf("执行交易失败: %w", err)
    }
    
    return result, nil
}

// ✅ 多返回值的错误处理
func getKeyPair(keyType string) (publicKey, privateKey string, err error) {
    switch keyType {
    case "ECDSA":
        return generateECDSAKeyPair()
    case "RSA":
        return generateRSAKeyPair()
    default:
        return "", "", fmt.Errorf("不支持的密钥类型: %s", keyType)
    }
}
```

### 2. 资源清理和错误处理

```go
// ✅ 使用 defer 进行资源清理
func processFile(filename string) (err error) {
    file, err := os.Open(filename)
    if err != nil {
        return fmt.Errorf("打开文件失败: %w", err)
    }
    defer func() {
        if closeErr := file.Close(); closeErr != nil {
            if err == nil {
                err = fmt.Errorf("关闭文件失败: %w", closeErr)
            } else {
                // 记录关闭错误，但保留原始错误
                log.Printf("关闭文件时发生错误: %v", closeErr)
            }
        }
    }()
    
    // 处理文件内容
    return processFileContent(file)
}
```

### 3. 并发场景的错误处理

```go
// ✅ 使用 errgroup 处理并发错误
import "golang.org/x/sync/errgroup"

func batchSignMessages(messages []string) ([]string, error) {
    var g errgroup.Group
    signatures := make([]string, len(messages))
    
    for i, msg := range messages {
        i, msg := i, msg // 避免闭包问题
        g.Go(func() error {
            sig, err := signMessage(msg)
            if err != nil {
                return fmt.Errorf("签名消息 %d 失败: %w", i, err)
            }
            signatures[i] = sig
            return nil
        })
    }
    
    if err := g.Wait(); err != nil {
        return nil, err
    }
    
    return signatures, nil
}

## 项目改进建议

### 1. 当前项目中的错误处理改进

**改进 SSM 模块的错误处理：**

```go
// 当前代码 (ssm/ecdsa.go)
func (e *EcdsaSigner) Sign(privateKey string, messageHex string) (string, error) {
    ecdsaPrivateKey, err := crypto.HexToECDSA(privateKey)
    if err != nil {
        return "", fmt.Errorf("无效的十六进制私钥: %w", err)
    }
    // ...
}

// ✅ 改进建议：使用自定义错误类型
type SSMError struct {
    Operation string
    CryptoType string
    Field     string
    Err       error
}

func (e *SSMError) Error() string {
    return fmt.Sprintf("SSM错误 [%s:%s:%s]: %v",
        e.Operation, e.CryptoType, e.Field, e.Err)
}

func (e *SSMError) Unwrap() error { return e.Err }

// 改进后的签名方法
func (e *EcdsaSigner) Sign(privateKey string, messageHex string) (string, error) {
    if privateKey == "" {
        return "", &SSMError{
            Operation: "sign",
            CryptoType: "ECDSA",
            Field: "privateKey",
            Err: errors.New("私钥不能为空"),
        }
    }

    ecdsaPrivateKey, err := crypto.HexToECDSA(privateKey)
    if err != nil {
        return "", &SSMError{
            Operation: "sign",
            CryptoType: "ECDSA",
            Field: "privateKey",
            Err: fmt.Errorf("解析私钥失败: %w", err),
        }
    }

    // 继续处理...
}
```

**改进 RPC 服务的错误处理：**

```go
// 当前代码 (service/rpc.go)
func (r *RpcServer) SignTxMessage(ctx context.Context, request *pb_wallet.SignTxMessageRequest) (*pb_wallet.SignTxMessageResponse, error) {
    privateKey := r.db.Get(request.PublicKey)
    if privateKey == "" {
        return &pb_wallet.SignTxMessageResponse{
            Code: pb_wallet.EReturnCode_ERROR,
            Msg:  "public key not found",
        }, nil
    }
    // ...
}

// ✅ 改进建议：统一错误处理
type RPCError struct {
    Code    pb_wallet.EReturnCode
    Message string
    Details error
}

func (e *RPCError) Error() string {
    if e.Details != nil {
        return fmt.Sprintf("%s: %v", e.Message, e.Details)
    }
    return e.Message
}

func (r *RpcServer) handleError(err error, defaultMsg string) *pb_wallet.SignTxMessageResponse {
    var rpcErr *RPCError
    if errors.As(err, &rpcErr) {
        return &pb_wallet.SignTxMessageResponse{
            Code: rpcErr.Code,
            Msg:  rpcErr.Message,
        }
    }

    // 记录详细错误日志
    log.Error("RPC操作失败", "error", err, "default_msg", defaultMsg)

    return &pb_wallet.SignTxMessageResponse{
        Code: pb_wallet.EReturnCode_ERROR,
        Msg:  defaultMsg,
    }
}

// 改进后的签名方法
func (r *RpcServer) SignTxMessage(ctx context.Context, request *pb_wallet.SignTxMessageRequest) (*pb_wallet.SignTxMessageResponse, error) {
    // 参数验证
    if request.PublicKey == "" {
        err := &RPCError{
            Code:    pb_wallet.EReturnCode_ERROR,
            Message: "公钥不能为空",
        }
        return r.handleError(err, "参数验证失败"), nil
    }

    // 查找私钥
    privateKey := r.db.Get(request.PublicKey)
    if privateKey == "" {
        err := &RPCError{
            Code:    pb_wallet.EReturnCode_ERROR,
            Message: "未找到对应的私钥",
        }
        return r.handleError(err, "密钥查找失败"), nil
    }

    // 执行签名
    signature, err := r.getSigner(request.Type).Sign(privateKey, request.MessageHash)
    if err != nil {
        rpcErr := &RPCError{
            Code:    pb_wallet.EReturnCode_ERROR,
            Message: "签名操作失败",
            Details: err,
        }
        return r.handleError(rpcErr, "签名失败"), nil
    }

    return &pb_wallet.SignTxMessageResponse{
        Code: pb_wallet.EReturnCode_SUCCESS,
        Msg:  "签名成功",
        Data: signature,
    }, nil
}
```

### 2. 数据库操作的错误处理

```go
// ✅ 改进数据库操作错误处理
type DatabaseError struct {
    Operation string
    Key       string
    Err       error
}

func (e *DatabaseError) Error() string {
    return fmt.Sprintf("数据库操作失败 [%s:%s]: %v", e.Operation, e.Key, e.Err)
}

func (e *DatabaseError) Unwrap() error { return e.Err }

// 改进数据库接口
type Database interface {
    Get(key string) (string, error)
    Set(key, value string) error
    Delete(key string) error
}

// 实现改进的数据库操作
func (db *LevelDB) Get(key string) (string, error) {
    if key == "" {
        return "", &DatabaseError{
            Operation: "get",
            Key:       key,
            Err:       errors.New("键不能为空"),
        }
    }

    value, err := db.db.Get([]byte(key), nil)
    if err != nil {
        if errors.Is(err, leveldb.ErrNotFound) {
            return "", &DatabaseError{
                Operation: "get",
                Key:       key,
                Err:       errors.New("键不存在"),
            }
        }
        return "", &DatabaseError{
            Operation: "get",
            Key:       key,
            Err:       fmt.Errorf("读取失败: %w", err),
        }
    }

    return string(value), nil
}
```

### 3. 配置和初始化的错误处理

```go
// ✅ 改进配置验证
type ConfigError struct {
    Field string
    Value string
    Err   error
}

func (e *ConfigError) Error() string {
    return fmt.Sprintf("配置错误 [%s=%s]: %v", e.Field, e.Value, e.Err)
}

func ValidateConfig(cfg *Config) error {
    var errs []error

    if cfg.Host == "" {
        errs = append(errs, &ConfigError{
            Field: "host",
            Value: cfg.Host,
            Err:   errors.New("主机地址不能为空"),
        })
    }

    if cfg.Port <= 0 || cfg.Port > 65535 {
        errs = append(errs, &ConfigError{
            Field: "port",
            Value: fmt.Sprintf("%d", cfg.Port),
            Err:   errors.New("端口号必须在1-65535之间"),
        })
    }

    if cfg.AesKey == "" {
        errs = append(errs, &ConfigError{
            Field: "aes_key",
            Value: "***",
            Err:   errors.New("AES密钥不能为空"),
        })
    }

    if len(errs) > 0 {
        return fmt.Errorf("配置验证失败: %v", errs)
    }

    return nil
}

## 工具和库推荐

### 1. 第三方错误处理库

**github.com/pkg/errors (已弃用，推荐使用标准库):**

```go
// 旧的做法 (不推荐)
import "github.com/pkg/errors"

func oldWay() error {
    err := doSomething()
    if err != nil {
        return errors.Wrap(err, "操作失败")
    }
    return nil
}

// ✅ 新的做法 (推荐使用标准库)
func newWay() error {
    err := doSomething()
    if err != nil {
        return fmt.Errorf("操作失败: %w", err)
    }
    return nil
}
```

**github.com/hashicorp/go-multierror:**

```go
import "github.com/hashicorp/go-multierror"

// ✅ 处理多个错误
func validateMultipleFields(data *Data) error {
    var result *multierror.Error

    if data.Name == "" {
        result = multierror.Append(result, errors.New("名称不能为空"))
    }

    if data.Email == "" {
        result = multierror.Append(result, errors.New("邮箱不能为空"))
    }

    if !isValidEmail(data.Email) {
        result = multierror.Append(result, errors.New("邮箱格式无效"))
    }

    return result.ErrorOrNil()
}
```

**golang.org/x/sync/errgroup:**

```go
import "golang.org/x/sync/errgroup"

// ✅ 并发错误处理
func processBatch(items []Item) error {
    g, ctx := errgroup.WithContext(context.Background())

    for _, item := range items {
        item := item // 避免闭包问题
        g.Go(func() error {
            return processItem(ctx, item)
        })
    }

    return g.Wait()
}
```

### 2. 日志记录与错误处理

**结构化日志记录:**

```go
import (
    "log/slog"
    "context"
)

// ✅ 结构化错误日志
func logError(ctx context.Context, err error, msg string, fields ...any) {
    logger := slog.Default()

    // 提取错误链信息
    var errorChain []string
    for e := err; e != nil; e = errors.Unwrap(e) {
        errorChain = append(errorChain, e.Error())
    }

    // 记录结构化日志
    logger.ErrorContext(ctx, msg,
        append(fields,
            "error", err.Error(),
            "error_chain", errorChain,
            "error_type", fmt.Sprintf("%T", err),
        )...,
    )
}

// 使用示例
func handleRequest(ctx context.Context, req *Request) error {
    err := processRequest(req)
    if err != nil {
        logError(ctx, err, "处理请求失败",
            "request_id", req.ID,
            "user_id", req.UserID,
        )
        return err
    }
    return nil
}
```

**错误监控和告警:**

```go
// ✅ 错误监控接口
type ErrorMonitor interface {
    RecordError(ctx context.Context, err error, tags map[string]string)
    RecordMetric(name string, value float64, tags map[string]string)
}

// 实现错误监控
type MonitoringWrapper struct {
    monitor ErrorMonitor
    next    Handler
}

func (m *MonitoringWrapper) Handle(ctx context.Context, req *Request) error {
    start := time.Now()
    err := m.next.Handle(ctx, req)

    duration := time.Since(start)
    tags := map[string]string{
        "operation": req.Operation,
        "status":    "success",
    }

    if err != nil {
        tags["status"] = "error"
        tags["error_type"] = fmt.Sprintf("%T", err)
        m.monitor.RecordError(ctx, err, tags)
    }

    m.monitor.RecordMetric("request_duration_ms",
        float64(duration.Milliseconds()), tags)

    return err
}
```

### 3. 错误处理中间件

```go
// ✅ HTTP 错误处理中间件
func ErrorHandlingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        defer func() {
            if err := recover(); err != nil {
                log.Printf("Panic recovered: %v", err)
                http.Error(w, "Internal Server Error",
                    http.StatusInternalServerError)
            }
        }()

        next.ServeHTTP(w, r)
    })
}

// ✅ gRPC 错误处理拦截器
func ErrorInterceptor(ctx context.Context, req interface{},
    info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {

    resp, err := handler(ctx, req)
    if err != nil {
        // 记录错误
        log.Printf("gRPC error in %s: %v", info.FullMethod, err)

        // 转换为 gRPC 状态码
        if errors.Is(err, ErrInvalidInput) {
            return nil, status.Error(codes.InvalidArgument, err.Error())
        }
        if errors.Is(err, ErrNotFound) {
            return nil, status.Error(codes.NotFound, err.Error())
        }

        return nil, status.Error(codes.Internal, "内部服务器错误")
    }

    return resp, nil
}
```

## 性能考虑

### 1. 错误处理的性能影响

**避免过度的错误检查:**

```go
// ❌ 性能较差：每次都创建新的错误
func badPerformance(data []byte) error {
    for i, b := range data {
        if b == 0 {
            return fmt.Errorf("在位置 %d 发现零字节", i) // 每次都分配内存
        }
    }
    return nil
}

// ✅ 性能更好：使用预定义错误
var ErrZeroByte = errors.New("发现零字节")

func goodPerformance(data []byte) error {
    for _, b := range data {
        if b == 0 {
            return ErrZeroByte // 重用预定义错误
        }
    }
    return nil
}
```

**使用错误池减少内存分配:**

```go
// ✅ 错误对象池
var errorPool = sync.Pool{
    New: func() interface{} {
        return &CustomError{}
    },
}

type CustomError struct {
    Code    int
    Message string
    Details string
}

func (e *CustomError) Error() string {
    return fmt.Sprintf("[%d] %s: %s", e.Code, e.Message, e.Details)
}

func (e *CustomError) Reset() {
    e.Code = 0
    e.Message = ""
    e.Details = ""
}

// 使用错误池
func createError(code int, msg, details string) error {
    err := errorPool.Get().(*CustomError)
    err.Code = code
    err.Message = msg
    err.Details = details

    // 注意：使用完后需要归还到池中
    defer func() {
        err.Reset()
        errorPool.Put(err)
    }()

    return err
}
```

### 2. 错误处理的基准测试

```go
// ✅ 错误处理性能测试
func BenchmarkErrorHandling(b *testing.B) {
    b.Run("WithError", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            _, err := functionThatReturnsError()
            if err != nil {
                // 处理错误
            }
        }
    })

    b.Run("WithoutError", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            _ = functionThatNeverErrors()
        }
    })
}
```

### 3. 快速失败原则

```go
// ✅ 快速失败，避免不必要的计算
func processRequest(req *Request) (*Response, error) {
    // 1. 首先进行快速验证
    if req == nil {
        return nil, ErrNilRequest
    }

    if req.ID == "" {
        return nil, ErrEmptyID
    }

    // 2. 然后进行较重的操作
    data, err := heavyComputation(req)
    if err != nil {
        return nil, fmt.Errorf("计算失败: %w", err)
    }

    return &Response{Data: data}, nil
}
```

## 总结

1. **优先使用标准库**：Go 1.13+ 的错误处理功能已经足够强大
2. **创建有意义的错误**：包含足够的上下文信息
3. **使用错误包装**：保持错误链的完整性
4. **避免忽略错误**：每个错误都应该被适当处理
5. **性能优化**：在高频路径上避免不必要的错误分配
6. **结构化日志**：便于错误追踪和调试
7. **监控和告警**：及时发现和处理生产环境中的错误

通过遵循这些最佳实践，可以构建更加健壮、可维护和高性能的 Go 应用程序。
```
```
