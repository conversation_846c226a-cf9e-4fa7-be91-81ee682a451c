package config

import (
	"wallet-sign-common/config"
)

type DBConfig struct {
	Location string `mapstructure:"location"`
}

type Config struct {
	Rpc                             *config.RpcConfig `mapstructure:"rpc"`
	DB                              *DBConfig         `mapstructure:"db"`
	MaxExportKeyPairCountPerRequest uint64            `mapstructure:"max_export_key_pair_count_per_request"`
	AesKey                          string            `mapstructure:"aes_key"`
	BearerToken                     string            `mapstructure:"bearer_token"`
}

func NewConfig(path string) (*Config, error) {
	var cfg Config
	return config.ReadConfig(path, &cfg)
}
