package ssm_test

import (
	"github.com/ethereum/go-ethereum/log"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"wallet-sign-signer/ssm"
)

var rsa = ssm.Factory(ssm.RSA)

func TestRSASigner_GenerateKeyPair(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stderr, log.LevelInfo, true)))

	pair, err := rsa.GenerateKeyPair()
	assert.NoError(t, err)

	log.Info(
		"GenerateKeyPair",
		"privateKey", pair.PrivateKey,
		"publicKey", pair.PublicKey,
		"compressedPublicKey", pair.CompressedPublicKey,
	)

	assert.IsType(t, pair.PublicKey, string(""))
	assert.IsType(t, pair.CompressedPublicKey, string(""))
	assert.IsType(t, pair.PrivateKey, string(""))
	// RSA 没有压缩公钥的概念，所以应该是空的或者与公钥相同
	assert.Equal(t, pair.PublicKey, pair.CompressedPublicKey)
}

func TestRSASigner_Sign(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stdout, log.LevelInfo, true)))

	// 首先生成一个密钥对
	pair, err := rsa.GenerateKeyPair()
	assert.NoError(t, err)

	// 使用十六进制消息哈希（与 ECDSA 和 EdDSA 保持一致）
	messageHex := "123456"
	signedMessage, err := rsa.Sign(pair.PrivateKey, messageHex)
	assert.NoError(t, err)
	assert.IsType(t, signedMessage, string(""))
	assert.NotEmpty(t, signedMessage)

	log.Info(
		"Sign",
		"messageHex", messageHex,
		"signedMessage", signedMessage,
	)
}

func TestRSASigner_VerifySignature(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stdout, log.LevelInfo, true)))

	// 生成密钥对
	pair, err := rsa.GenerateKeyPair()
	assert.NoError(t, err)

	messageHex := "123456"

	// 签名
	signature, err := rsa.Sign(pair.PrivateKey, messageHex)
	assert.NoError(t, err)
	assert.NotEmpty(t, signature)

	// 验证签名
	isValid, err := rsa.VerifySignature(pair.PublicKey, messageHex, signature)
	assert.NoError(t, err)
	assert.True(t, isValid)

	// 验证错误的签名
	wrongSignature := "dGVzdCBzaWduYXR1cmU=" // base64 编码的 "test signature"
	isValidWrong, err := rsa.VerifySignature(pair.PublicKey, messageHex, wrongSignature)
	assert.NoError(t, err)
	assert.False(t, isValidWrong)

	// 验证错误的消息
	wrongMessage := "654321"
	isValidWrongMsg, err := rsa.VerifySignature(pair.PublicKey, wrongMessage, signature)
	assert.NoError(t, err)
	assert.False(t, isValidWrongMsg)

	log.Info(
		"VerifySignature",
		"messageHex", messageHex,
		"signature", signature,
		"isValid", isValid,
		"isValidWrong", isValidWrong,
		"isValidWrongMsg", isValidWrongMsg,
	)
}
