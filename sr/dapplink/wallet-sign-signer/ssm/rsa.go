package ssm

import (
	"crypto"
	"crypto/rand"
	rsa2 "crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"fmt"
)

type RSASigner struct {
}

func (R RSASigner) GenerateKeyPair() (*Keypair, error) {
	privateKey, err := rsa2.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, err
	}

	publicKey := &privateKey.PublicKey

	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return nil, err
	}

	publicKeyBase64 := base64.StdEncoding.EncodeToString(publicKeyBytes)
	return &Keypair{
		PrivateKey:          base64.StdEncoding.EncodeToString(privateKeyBytes),
		PublicKey:           publicKeyBase64,
		CompressedPublicKey: publicKeyBase64, // RSA 没有压缩公钥概念，使用相同值
	}, nil
}

func (R RSASigner) Sign(privateKey string, messageHashHex string) (string, error) {
	privateKeyBytes, err := base64.StdEncoding.DecodeString(privateKey)
	if err != nil {
		return "", err
	}

	priv, err := x509.ParsePKCS1PrivateKey(privateKeyBytes)
	if err != nil {
		return "", err
	}

	// 将十六进制字符串转换为字节数组（与 ECDSA 和 EdDSA 保持一致）
	messageBytes, err := hex.DecodeString(messageHashHex)
	if err != nil {
		return "", err
	}

	// 对消息进行 SHA256 哈希
	hash := sha256.Sum256(messageBytes)

	// 使用 SHA256 哈希算法进行签名
	signature, err := rsa2.SignPKCS1v15(rand.Reader, priv, crypto.SHA256, hash[:])
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

func (R RSASigner) VerifySignature(publicKeyHex string, messageHashHex string, signatureHex string) (bool, error) {
	// 1. 将 Base64 编码的公钥解码为字节数组
	publicKeyBytes, err := base64.StdEncoding.DecodeString(publicKeyHex)
	if err != nil {
		return false, fmt.Errorf("无效的 Base64 公钥: %w", err)
	}

	// 2. 解析公钥
	publicKeyInterface, err := x509.ParsePKIXPublicKey(publicKeyBytes)
	if err != nil {
		return false, fmt.Errorf("解析公钥失败: %w", err)
	}

	publicKey, ok := publicKeyInterface.(*rsa2.PublicKey)
	if !ok {
		return false, fmt.Errorf("公钥不是 RSA 公钥")
	}

	// 3. 将十六进制字符串转换为字节数组（与签名时保持一致）
	messageBytes, err := hex.DecodeString(messageHashHex)
	if err != nil {
		return false, fmt.Errorf("无效的十六进制消息: %w", err)
	}

	// 4. 对消息进行 SHA256 哈希（与签名时保持一致）
	hash := sha256.Sum256(messageBytes)

	// 5. 将 Base64 编码的签名解码为字节数组
	signatureBytes, err := base64.StdEncoding.DecodeString(signatureHex)
	if err != nil {
		return false, fmt.Errorf("无效的 Base64 签名: %w", err)
	}

	// 6. 验证签名
	err = rsa2.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signatureBytes)
	return err == nil, nil
}
