package service

import (
	"context"
	"github.com/ethereum/go-ethereum/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"wallet-sign-common/rpc"
	"wallet-sign-go/config"
	"wallet-sign-go/database"
	"wallet-sign-go/protobuf/generated"
	"wallet-sign-go/ssm"
)

type RpcServer struct {
	config *config.Config
	db     *database.Database
	pb.UnimplementedWalletServiceServer
}

func (r *RpcServer) GetSupportSignWay(ctx context.Context, request *pb.GetSupportSignWayRequest) (*pb.GetSupportSignWayResponse, error) {
	return &pb.GetSupportSignWayResponse{
		Code: pb.EReturnCode_SUCCESS,
		Msg:  "",
		Data: []string{
			pb.ECryptoType_ECDSA.String(),
			pb.ECryptoType_EDDSA.String(),
			pb.ECryptoType_RSA.String(),
		},
	}, nil
}

func (r *RpcServer) ExportPublicKeyList(ctx context.Context, request *pb.ExportPublicKeyListRequest) (*pb.ExportPublicKeyListResponse, error) {
	if request.Count > r.config.MaxExportKeyPairCountPerRequest {
		return &pb.ExportPublicKeyListResponse{
			Code: pb.EReturnCode_ERROR,
			Msg:  "count is too large",
		}, nil
	}

	var retKeypairList = make([]*pb.PublicKey, 0, request.Count)

	var keypairList = make([]*ssm.Keypair, 0, request.Count)
	s := ssm.Factory(request.Type)
	for i := 0; uint64(i) < request.Count; i++ {
		keypair, err := s.GenerateKeyPair()
		if err != nil {
			log.Error("An error occurred while generating keypair", "err", err)
			return &pb.ExportPublicKeyListResponse{
				Code: pb.EReturnCode_ERROR,
				Msg:  "An error occurred while generating keypair",
			}, err
		}

		keypairList = append(keypairList, keypair)
		retKeypairList = append(retKeypairList, &pb.PublicKey{
			PublicKey:           keypair.PublicKey,
			CompressedPublicKey: keypair.CompressedPublicKey,
		})
	}

	for _, keypair := range keypairList {
		err := r.db.Store(keypair.PublicKey, keypair.PrivateKey)
		if err != nil {
			return nil, err
		}
	}

	return &pb.ExportPublicKeyListResponse{
		Code: pb.EReturnCode_SUCCESS,
		Msg:  "",
		Data: retKeypairList,
	}, nil
}

func (r *RpcServer) SignTxMessage(ctx context.Context, request *pb.SignTxMessageRequest) (*pb.SignTxMessageResponse, error) {
	privateKey := r.db.Get(request.PublicKey)
	if privateKey == "" {
		return &pb.SignTxMessageResponse{
			Code: pb.EReturnCode_ERROR,
			Msg:  "public key not found",
		}, nil
	}

	signedMessage, err := ssm.Factory(request.Type).Sign(privateKey, request.MessageHash)

	if err != nil {
		log.Error("An error occurred while signing the message", "err", err.Error())
		return &pb.SignTxMessageResponse{
			Code: pb.EReturnCode_ERROR,
			Msg:  "An error occurred while signing the message",
			Data: signedMessage,
		}, nil
	}

	return &pb.SignTxMessageResponse{
		Code: pb.EReturnCode_SUCCESS,
		Msg:  "",
		Data: signedMessage,
	}, nil
}

func (r *RpcServer) SignTxMessageInBatches(ctx context.Context, request *pb.SignTxMessageInBatchesRequest) (*pb.SignTxMessageInBatchesResponse, error) {
	resList := make([]*pb.SignTxMessageResponse, len(request.List))
	retRes := &pb.SignTxMessageInBatchesResponse{
		Code: pb.EReturnCode_ERROR,
		Msg:  "",
		Data: nil,
	}

	hasFailed := false
	for i, x := range request.List {
		res, _ := r.SignTxMessage(ctx, x)
		resList[i] = res
		hasFailed = hasFailed || res.Code != pb.EReturnCode_SUCCESS
	}

	if !hasFailed {
		retRes.Code = pb.EReturnCode_SUCCESS
	}

	retRes.Data = resList

	return retRes, nil
}

func (r *RpcServer) checkAuth(ctx context.Context) error {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return status.Error(codes.Unauthenticated, "missing metadata")
	}

	tokens := md.Get("authorization")
	if len(tokens) == 0 {
		return status.Error(codes.Unauthenticated, "missing authorization token")
	}

	token := tokens[0]
	if token != "Bearer "+r.config.BearerToken {
		return status.Error(codes.Unauthenticated, "invalid token")
	}

	return nil
}

func (r *RpcServer) unaryServerInterceptor(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp any, err error) {
	if err := r.checkAuth(ctx); err != nil {
		return nil, err
	}

	return handler(ctx, req)
}

func (r *RpcServer) Start() error {
	return rpc.StartServer(r.config.Rpc, func(s *grpc.Server) error {
		pb.RegisterWalletServiceServer(s, r)
		return nil
	})
}

func NewRpcServer(config *config.Config) (*RpcServer, error) {
	db, err := database.NewDB(config)
	if err != nil {
		return nil, err
	}

	return &RpcServer{
		config: config,
		db:     db,
	}, nil
}
