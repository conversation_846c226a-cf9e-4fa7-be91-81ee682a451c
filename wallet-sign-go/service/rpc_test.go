package service_test

import (
	"context"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"log"
	"testing"
	"wallet-sign-go/protobuf/generated"
)

var client pb.WalletServiceClient

func init() {
	creds, err := credentials.NewClientTLSFromFile("../certs/server.crt", "")
	if err != nil {
		log.Fatalf("Failed to generate credentials %v", err)
	}

	conn, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithUnaryInterceptor(unaryClientInterceptor),
		grpc.WithTransportCredentials(creds),
	)
	if err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}

	client = pb.NewWalletServiceClient(conn)
}

func assertSuccess[T interface{ GetCode() pb.EReturnCode }](t *testing.T, res T) {
	assert.Equal(t, res.GetCode(), pb.EReturnCode_SUCCESS)
}

func createAuthContext(ctx context.Context) context.Context {
	md := metadata.Pairs(
		"authorization", "Bearer 12345",
	)
	return metadata.NewOutgoingContext(ctx, md)
}

func unaryClientInterceptor(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	return invoker(createAuthContext(ctx), method, req, reply, cc, opts...)
}

func TestRpcServer_ExportPublicKeyList(t *testing.T) {
	count := uint64(10000)
	result, err := client.ExportPublicKeyList(context.Background(), &pb.ExportPublicKeyListRequest{
		Type:  pb.ECryptoType_ECDSA,
		Count: count,
	})

	assertSuccess(t, result)
	assert.NoError(t, err)
	assert.IsType(t, &pb.ExportPublicKeyListResponse{}, result)
	assert.Len(t, result.Data, int(count))
}

func TestRpcServer_GetSupportSignWay(t *testing.T) {
	result, err := client.GetSupportSignWay(context.Background(), &pb.GetSupportSignWayRequest{})
	assertSuccess(t, result)
	assert.NoError(t, err)
	assert.Len(t, result.Data, 3)
	assert.Equal(t, result.Data, []string{
		pb.ECryptoType_ECDSA.String(),
		pb.ECryptoType_EDDSA.String(),
		pb.ECryptoType_RSA.String(),
	})
}

func TestRpcServer_SignTxMessage(t *testing.T) {
	resPubKeyList, _ := client.ExportPublicKeyList(context.Background(), &pb.ExportPublicKeyListRequest{
		Type:  pb.ECryptoType_ECDSA,
		Count: 1,
	})

	publicKey := resPubKeyList.Data[0].PublicKey
	messageHash := "123456"

	result, err := client.SignTxMessage(context.Background(), &pb.SignTxMessageRequest{
		Type:        pb.ECryptoType_ECDSA,
		PublicKey:   publicKey,
		MessageHash: messageHash,
	})
	assertSuccess(t, result)
	assert.NoError(t, err)
	assert.NotEqual(t, result.Data, "")
	assert.IsType(t, result.Data, string(""))
}

func TestRpcServer_SignTxMessageInBatches(t *testing.T) {
	resPubKeyList, _ := client.ExportPublicKeyList(context.Background(), &pb.ExportPublicKeyListRequest{
		Type:  pb.ECryptoType_ECDSA,
		Count: 2,
	})

	req := &pb.SignTxMessageInBatchesRequest{
		List: []*pb.SignTxMessageRequest{
			{
				Type:        pb.ECryptoType_ECDSA,
				PublicKey:   resPubKeyList.Data[0].PublicKey,
				MessageHash: "12345",
			},
			{
				Type:        pb.ECryptoType_ECDSA,
				PublicKey:   resPubKeyList.Data[1].PublicKey,
				MessageHash: "123456",
			},
		},
	}

	res, err := client.SignTxMessageInBatches(context.Background(), req)

	assertSuccess(t, res)
	assert.NoError(t, err)
	assert.Len(t, res.Data, 2)
	for i := range res.Data {
		assertSuccess(t, res.Data[i])
		assert.NoError(t, err)
		assert.NotEqual(t, res.Data[i].Data, "")
		assert.IsType(t, res.Data[i].Data, string(""))
	}
}
