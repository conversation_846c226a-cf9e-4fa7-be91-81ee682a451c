// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pb.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EReturnCode int32

const (
	EReturnCode_ERROR   EReturnCode = 0
	EReturnCode_SUCCESS EReturnCode = 1
)

// Enum value maps for EReturnCode.
var (
	EReturnCode_name = map[int32]string{
		0: "ERROR",
		1: "SUCCESS",
	}
	EReturnCode_value = map[string]int32{
		"ERROR":   0,
		"SUCCESS": 1,
	}
)

func (x EReturnCode) Enum() *EReturnCode {
	p := new(EReturnCode)
	*p = x
	return p
}

func (x EReturnCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EReturnCode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_proto_enumTypes[0].Descriptor()
}

func (EReturnCode) Type() protoreflect.EnumType {
	return &file_pb_proto_enumTypes[0]
}

func (x EReturnCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EReturnCode.Descriptor instead.
func (EReturnCode) EnumDescriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{0}
}

type ECryptoType int32

const (
	ECryptoType_ECDSA ECryptoType = 0
	ECryptoType_EDDSA ECryptoType = 1
	ECryptoType_RSA   ECryptoType = 2
)

// Enum value maps for ECryptoType.
var (
	ECryptoType_name = map[int32]string{
		0: "ECDSA",
		1: "EDDSA",
		2: "RSA",
	}
	ECryptoType_value = map[string]int32{
		"ECDSA": 0,
		"EDDSA": 1,
		"RSA":   2,
	}
)

func (x ECryptoType) Enum() *ECryptoType {
	p := new(ECryptoType)
	*p = x
	return p
}

func (x ECryptoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ECryptoType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_proto_enumTypes[1].Descriptor()
}

func (ECryptoType) Type() protoreflect.EnumType {
	return &file_pb_proto_enumTypes[1]
}

func (x ECryptoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ECryptoType.Descriptor instead.
func (ECryptoType) EnumDescriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{1}
}

type GetSupportSignWayRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportSignWayRequest) Reset() {
	*x = GetSupportSignWayRequest{}
	mi := &file_pb_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportSignWayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportSignWayRequest) ProtoMessage() {}

func (x *GetSupportSignWayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportSignWayRequest.ProtoReflect.Descriptor instead.
func (*GetSupportSignWayRequest) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{0}
}

type GetSupportSignWayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          EReturnCode            `protobuf:"varint,1,opt,name=Code,proto3,enum=EReturnCode" json:"Code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	Data          []string               `protobuf:"bytes,3,rep,name=Data,proto3" json:"Data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportSignWayResponse) Reset() {
	*x = GetSupportSignWayResponse{}
	mi := &file_pb_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportSignWayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportSignWayResponse) ProtoMessage() {}

func (x *GetSupportSignWayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportSignWayResponse.ProtoReflect.Descriptor instead.
func (*GetSupportSignWayResponse) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{1}
}

func (x *GetSupportSignWayResponse) GetCode() EReturnCode {
	if x != nil {
		return x.Code
	}
	return EReturnCode_ERROR
}

func (x *GetSupportSignWayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetSupportSignWayResponse) GetData() []string {
	if x != nil {
		return x.Data
	}
	return nil
}

type ExportPublicKeyListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          ECryptoType            `protobuf:"varint,1,opt,name=type,proto3,enum=ECryptoType" json:"type,omitempty"`
	Count         uint64                 `protobuf:"varint,2,opt,name=Count,proto3" json:"Count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExportPublicKeyListRequest) Reset() {
	*x = ExportPublicKeyListRequest{}
	mi := &file_pb_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportPublicKeyListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPublicKeyListRequest) ProtoMessage() {}

func (x *ExportPublicKeyListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPublicKeyListRequest.ProtoReflect.Descriptor instead.
func (*ExportPublicKeyListRequest) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{2}
}

func (x *ExportPublicKeyListRequest) GetType() ECryptoType {
	if x != nil {
		return x.Type
	}
	return ECryptoType_ECDSA
}

func (x *ExportPublicKeyListRequest) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ExportPublicKeyListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          EReturnCode            `protobuf:"varint,1,opt,name=Code,proto3,enum=EReturnCode" json:"Code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	Data          []*PublicKey           `protobuf:"bytes,3,rep,name=Data,proto3" json:"Data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExportPublicKeyListResponse) Reset() {
	*x = ExportPublicKeyListResponse{}
	mi := &file_pb_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportPublicKeyListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPublicKeyListResponse) ProtoMessage() {}

func (x *ExportPublicKeyListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPublicKeyListResponse.ProtoReflect.Descriptor instead.
func (*ExportPublicKeyListResponse) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{3}
}

func (x *ExportPublicKeyListResponse) GetCode() EReturnCode {
	if x != nil {
		return x.Code
	}
	return EReturnCode_ERROR
}

func (x *ExportPublicKeyListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ExportPublicKeyListResponse) GetData() []*PublicKey {
	if x != nil {
		return x.Data
	}
	return nil
}

type PublicKey struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	PublicKey           string                 `protobuf:"bytes,1,opt,name=PublicKey,proto3" json:"PublicKey,omitempty"`
	CompressedPublicKey string                 `protobuf:"bytes,2,opt,name=CompressedPublicKey,proto3" json:"CompressedPublicKey,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PublicKey) Reset() {
	*x = PublicKey{}
	mi := &file_pb_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKey) ProtoMessage() {}

func (x *PublicKey) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKey.ProtoReflect.Descriptor instead.
func (*PublicKey) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{4}
}

func (x *PublicKey) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *PublicKey) GetCompressedPublicKey() string {
	if x != nil {
		return x.CompressedPublicKey
	}
	return ""
}

type SignTxMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          ECryptoType            `protobuf:"varint,1,opt,name=type,proto3,enum=ECryptoType" json:"type,omitempty"`
	PublicKey     string                 `protobuf:"bytes,2,opt,name=PublicKey,proto3" json:"PublicKey,omitempty"`
	MessageHash   string                 `protobuf:"bytes,3,opt,name=MessageHash,proto3" json:"MessageHash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignTxMessageRequest) Reset() {
	*x = SignTxMessageRequest{}
	mi := &file_pb_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageRequest) ProtoMessage() {}

func (x *SignTxMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageRequest.ProtoReflect.Descriptor instead.
func (*SignTxMessageRequest) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{5}
}

func (x *SignTxMessageRequest) GetType() ECryptoType {
	if x != nil {
		return x.Type
	}
	return ECryptoType_ECDSA
}

func (x *SignTxMessageRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *SignTxMessageRequest) GetMessageHash() string {
	if x != nil {
		return x.MessageHash
	}
	return ""
}

type SignTxMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          EReturnCode            `protobuf:"varint,1,opt,name=Code,proto3,enum=EReturnCode" json:"Code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=Data,proto3" json:"Data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignTxMessageResponse) Reset() {
	*x = SignTxMessageResponse{}
	mi := &file_pb_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageResponse) ProtoMessage() {}

func (x *SignTxMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageResponse.ProtoReflect.Descriptor instead.
func (*SignTxMessageResponse) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{6}
}

func (x *SignTxMessageResponse) GetCode() EReturnCode {
	if x != nil {
		return x.Code
	}
	return EReturnCode_ERROR
}

func (x *SignTxMessageResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignTxMessageResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type SignTxMessageInBatchesRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	List          []*SignTxMessageRequest `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignTxMessageInBatchesRequest) Reset() {
	*x = SignTxMessageInBatchesRequest{}
	mi := &file_pb_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageInBatchesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageInBatchesRequest) ProtoMessage() {}

func (x *SignTxMessageInBatchesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageInBatchesRequest.ProtoReflect.Descriptor instead.
func (*SignTxMessageInBatchesRequest) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{7}
}

func (x *SignTxMessageInBatchesRequest) GetList() []*SignTxMessageRequest {
	if x != nil {
		return x.List
	}
	return nil
}

type SignTxMessageInBatchesResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          EReturnCode              `protobuf:"varint,1,opt,name=Code,proto3,enum=EReturnCode" json:"Code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	Data          []*SignTxMessageResponse `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignTxMessageInBatchesResponse) Reset() {
	*x = SignTxMessageInBatchesResponse{}
	mi := &file_pb_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignTxMessageInBatchesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignTxMessageInBatchesResponse) ProtoMessage() {}

func (x *SignTxMessageInBatchesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignTxMessageInBatchesResponse.ProtoReflect.Descriptor instead.
func (*SignTxMessageInBatchesResponse) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{8}
}

func (x *SignTxMessageInBatchesResponse) GetCode() EReturnCode {
	if x != nil {
		return x.Code
	}
	return EReturnCode_ERROR
}

func (x *SignTxMessageInBatchesResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignTxMessageInBatchesResponse) GetData() []*SignTxMessageResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_pb_proto protoreflect.FileDescriptor

const file_pb_proto_rawDesc = "" +
	"\n" +
	"\bpb.proto\"\x1a\n" +
	"\x18GetSupportSignWayRequest\"c\n" +
	"\x19GetSupportSignWayResponse\x12 \n" +
	"\x04Code\x18\x01 \x01(\x0e2\f.EReturnCodeR\x04Code\x12\x10\n" +
	"\x03Msg\x18\x02 \x01(\tR\x03Msg\x12\x12\n" +
	"\x04Data\x18\x03 \x03(\tR\x04Data\"T\n" +
	"\x1aExportPublicKeyListRequest\x12 \n" +
	"\x04type\x18\x01 \x01(\x0e2\f.ECryptoTypeR\x04type\x12\x14\n" +
	"\x05Count\x18\x02 \x01(\x04R\x05Count\"q\n" +
	"\x1bExportPublicKeyListResponse\x12 \n" +
	"\x04Code\x18\x01 \x01(\x0e2\f.EReturnCodeR\x04Code\x12\x10\n" +
	"\x03Msg\x18\x02 \x01(\tR\x03Msg\x12\x1e\n" +
	"\x04Data\x18\x03 \x03(\v2\n" +
	".PublicKeyR\x04Data\"[\n" +
	"\tPublicKey\x12\x1c\n" +
	"\tPublicKey\x18\x01 \x01(\tR\tPublicKey\x120\n" +
	"\x13CompressedPublicKey\x18\x02 \x01(\tR\x13CompressedPublicKey\"x\n" +
	"\x14SignTxMessageRequest\x12 \n" +
	"\x04type\x18\x01 \x01(\x0e2\f.ECryptoTypeR\x04type\x12\x1c\n" +
	"\tPublicKey\x18\x02 \x01(\tR\tPublicKey\x12 \n" +
	"\vMessageHash\x18\x03 \x01(\tR\vMessageHash\"_\n" +
	"\x15SignTxMessageResponse\x12 \n" +
	"\x04Code\x18\x01 \x01(\x0e2\f.EReturnCodeR\x04Code\x12\x10\n" +
	"\x03Msg\x18\x02 \x01(\tR\x03Msg\x12\x12\n" +
	"\x04Data\x18\x03 \x01(\tR\x04Data\"J\n" +
	"\x1dSignTxMessageInBatchesRequest\x12)\n" +
	"\x04list\x18\x01 \x03(\v2\x15.SignTxMessageRequestR\x04list\"\x80\x01\n" +
	"\x1eSignTxMessageInBatchesResponse\x12 \n" +
	"\x04Code\x18\x01 \x01(\x0e2\f.EReturnCodeR\x04Code\x12\x10\n" +
	"\x03Msg\x18\x02 \x01(\tR\x03Msg\x12*\n" +
	"\x04data\x18\x03 \x03(\v2\x16.SignTxMessageResponseR\x04data*%\n" +
	"\vEReturnCode\x12\t\n" +
	"\x05ERROR\x10\x00\x12\v\n" +
	"\aSUCCESS\x10\x01*,\n" +
	"\vECryptoType\x12\t\n" +
	"\x05ECDSA\x10\x00\x12\t\n" +
	"\x05EDDSA\x10\x01\x12\a\n" +
	"\x03RSA\x10\x022\xd0\x02\n" +
	"\rWalletService\x12L\n" +
	"\x11getSupportSignWay\x12\x19.GetSupportSignWayRequest\x1a\x1a.GetSupportSignWayResponse\"\x00\x12R\n" +
	"\x13exportPublicKeyList\x12\x1b.ExportPublicKeyListRequest\x1a\x1c.ExportPublicKeyListResponse\"\x00\x12@\n" +
	"\rsignTxMessage\x12\x15.SignTxMessageRequest\x1a\x16.SignTxMessageResponse\"\x00\x12[\n" +
	"\x16signTxMessageInBatches\x12\x1e.SignTxMessageInBatchesRequest\x1a\x1f.SignTxMessageInBatchesResponse\"\x00B\x19Z\x17./protobuf/generated;pbb\x06proto3"

var (
	file_pb_proto_rawDescOnce sync.Once
	file_pb_proto_rawDescData []byte
)

func file_pb_proto_rawDescGZIP() []byte {
	file_pb_proto_rawDescOnce.Do(func() {
		file_pb_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_proto_rawDesc), len(file_pb_proto_rawDesc)))
	})
	return file_pb_proto_rawDescData
}

var file_pb_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_pb_proto_goTypes = []any{
	(EReturnCode)(0),                       // 0: EReturnCode
	(ECryptoType)(0),                       // 1: ECryptoType
	(*GetSupportSignWayRequest)(nil),       // 2: GetSupportSignWayRequest
	(*GetSupportSignWayResponse)(nil),      // 3: GetSupportSignWayResponse
	(*ExportPublicKeyListRequest)(nil),     // 4: ExportPublicKeyListRequest
	(*ExportPublicKeyListResponse)(nil),    // 5: ExportPublicKeyListResponse
	(*PublicKey)(nil),                      // 6: PublicKey
	(*SignTxMessageRequest)(nil),           // 7: SignTxMessageRequest
	(*SignTxMessageResponse)(nil),          // 8: SignTxMessageResponse
	(*SignTxMessageInBatchesRequest)(nil),  // 9: SignTxMessageInBatchesRequest
	(*SignTxMessageInBatchesResponse)(nil), // 10: SignTxMessageInBatchesResponse
}
var file_pb_proto_depIdxs = []int32{
	0,  // 0: GetSupportSignWayResponse.Code:type_name -> EReturnCode
	1,  // 1: ExportPublicKeyListRequest.type:type_name -> ECryptoType
	0,  // 2: ExportPublicKeyListResponse.Code:type_name -> EReturnCode
	6,  // 3: ExportPublicKeyListResponse.Data:type_name -> PublicKey
	1,  // 4: SignTxMessageRequest.type:type_name -> ECryptoType
	0,  // 5: SignTxMessageResponse.Code:type_name -> EReturnCode
	7,  // 6: SignTxMessageInBatchesRequest.list:type_name -> SignTxMessageRequest
	0,  // 7: SignTxMessageInBatchesResponse.Code:type_name -> EReturnCode
	8,  // 8: SignTxMessageInBatchesResponse.data:type_name -> SignTxMessageResponse
	2,  // 9: WalletService.getSupportSignWay:input_type -> GetSupportSignWayRequest
	4,  // 10: WalletService.exportPublicKeyList:input_type -> ExportPublicKeyListRequest
	7,  // 11: WalletService.signTxMessage:input_type -> SignTxMessageRequest
	9,  // 12: WalletService.signTxMessageInBatches:input_type -> SignTxMessageInBatchesRequest
	3,  // 13: WalletService.getSupportSignWay:output_type -> GetSupportSignWayResponse
	5,  // 14: WalletService.exportPublicKeyList:output_type -> ExportPublicKeyListResponse
	8,  // 15: WalletService.signTxMessage:output_type -> SignTxMessageResponse
	10, // 16: WalletService.signTxMessageInBatches:output_type -> SignTxMessageInBatchesResponse
	13, // [13:17] is the sub-list for method output_type
	9,  // [9:13] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_proto_init() }
func file_pb_proto_init() {
	if File_pb_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_proto_rawDesc), len(file_pb_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_proto_goTypes,
		DependencyIndexes: file_pb_proto_depIdxs,
		EnumInfos:         file_pb_proto_enumTypes,
		MessageInfos:      file_pb_proto_msgTypes,
	}.Build()
	File_pb_proto = out.File
	file_pb_proto_goTypes = nil
	file_pb_proto_depIdxs = nil
}
