package ssm_test

import (
	"github.com/ethereum/go-ethereum/log"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"wallet-sign-go/ssm"
)

func TestEdDSASigner_Sign(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stdout, log.LevelInfo, true)))

	privateKey := "9972C6434DB41DB8D3F09C2AA155397C567D71A1B9AE3C7DA3470A9AD28F8DA7463CFCCE8E4C8E45A79D3895E281978D6BE98D7AF615A6310FDB0D3F986FF2BE"
	messageHex := "123456"
	signedMessage, err := ssm.Eddsa.Sign(privateKey, messageHex)
	log.Info(
		"Sign",
		"signedMessage", signedMessage,
	)

	assert.NoError(t, err)
	assert.IsType(t, signedMessage, string(""))

}

func TestEdDSASigner_GenerateKeyPair(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stderr, log.LevelInfo, true)))

	pair, err := ssm.Eddsa.GenerateKeyPair()
	assert.NoError(t, err)

	log.Info(
		"GenerateKeyPair",
		"privateKey", pair.PrivateKey,
		"publicKey", pair.PublicKey,
		"compressedPublicKey", pair.CompressedPublicKey,
	)

	assert.IsType(t, pair.PublicKey, string(""))
	assert.IsType(t, pair.CompressedPublicKey, string(""))
	assert.IsType(t, pair.PrivateKey, string(""))
	assert.Equal(t, pair.PublicKey, pair.CompressedPublicKey)
}

func TestEdDSASigner_VerifySignature(t *testing.T) {
	log.SetDefault(log.NewLogger(log.NewTerminalHandlerWithLevel(os.Stdout, log.LevelInfo, true)))

	// 生成密钥对
	pair, err := ssm.Eddsa.GenerateKeyPair()
	assert.NoError(t, err)

	messageHex := "123456"

	// 签名
	signature, err := ssm.Eddsa.Sign(pair.PrivateKey, messageHex)
	assert.NoError(t, err)

	// 验证签名
	isValid, err := ssm.Eddsa.VerifySignature(pair.PublicKey, messageHex, signature)
	assert.NoError(t, err)
	assert.True(t, isValid)

	// 验证错误的签名
	wrongSignature := "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
	isValidWrong, err := ssm.Eddsa.VerifySignature(pair.PublicKey, messageHex, wrongSignature)
	assert.NoError(t, err)
	assert.False(t, isValidWrong)

	// 验证错误的消息
	wrongMessage := "654321"
	isValidWrongMsg, err := ssm.Eddsa.VerifySignature(pair.PublicKey, wrongMessage, signature)
	assert.NoError(t, err)
	assert.False(t, isValidWrongMsg)

	log.Info(
		"VerifySignature",
		"messageHex", messageHex,
		"signature", signature,
		"isValid", isValid,
		"isValidWrong", isValidWrong,
		"isValidWrongMsg", isValidWrongMsg,
	)
}
