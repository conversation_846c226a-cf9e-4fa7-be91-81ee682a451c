package ssm

import (
	"fmt"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/status-im/keycard-go/hexutils"
)

type EcdsaSigner struct {
}

func (e *EcdsaSigner) Sign(privateKey string, messageHex string) (string, error) {
	// 1. 将十六进制字符串私钥解码为 *ecdsa.PrivateKey
	// crypto.HexToECDSA 会处理 "0x" 前缀（无论有无）

	ecdsaPrivateKey, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return "", fmt.Errorf("无效的十六进制私钥: %w", err)
	}

	// 2. 将十六进制字符串消息哈希解码为字节数组
	// 这里我们使用 common.HexToHash，因为它会返回一个 common.Hash 类型，
	// 这是 go-ethereum 中表示哈希的标准方式。
	// 它同样能处理 "0x" 前缀。
	messageHash := common.HexToHash(messageHex)

	// 3. 使用私钥对消息哈希进行签名
	// crypto.Sign 函数需要的是字节切片 ([]byte)，所以我们用 .Bytes() 方法
	signatureBytes, err := crypto.Sign(messageHash.Bytes(), ecdsaPrivateKey)
	if err != nil {
		return "", fmt.Errorf("签名失败: %w", err)
	}

	// 4. 将签名后的字节数组编码为十六进制字符串
	// 使用 hexutil.Encode 添加 "0x" 前缀，这是以太坊生态的惯例
	return hexutil.Encode(signatureBytes), nil
}

func (e *EcdsaSigner) GenerateKeyPair() (*Keypair, error) {
	key, err := crypto.GenerateKey()

	if err != nil {
		return nil, err
	}

	privateKey := hexutils.BytesToHex(crypto.FromECDSA(key))
	publicKey := hexutils.BytesToHex(crypto.FromECDSAPub(&key.PublicKey))
	compressedPublicKey := hexutils.BytesToHex(crypto.CompressPubkey(&key.PublicKey))

	return &Keypair{
		PrivateKey:          privateKey,
		PublicKey:           publicKey,
		CompressedPublicKey: compressedPublicKey,
	}, nil
}

func (e *EcdsaSigner) VerifySignature(publicKeyHex string, messageHashHex string, signatureHex string) (bool, error) {
	// 1. 将十六进制字符串公钥解码为字节数组
	publicKeyBytes := hexutils.HexToBytes(publicKeyHex)

	// 2. 将十六进制字符串消息哈希解码为字节数组
	messageHash := common.HexToHash(messageHashHex)

	// 3. 将十六进制字符串签名解码为字节数组
	signatureBytes, err := hexutil.Decode(signatureHex)
	if err != nil {
		return false, fmt.Errorf("无效的十六进制签名: %w", err)
	}

	// 4. 验证签名
	// crypto.VerifySignature 需要去掉恢复ID（最后一个字节）
	if len(signatureBytes) == 65 {
		signatureBytes = signatureBytes[:64] // 去掉恢复ID
	}

	// 直接使用公钥字节数组进行验证
	return crypto.VerifySignature(publicKeyBytes, messageHash.Bytes(), signatureBytes), nil
}
