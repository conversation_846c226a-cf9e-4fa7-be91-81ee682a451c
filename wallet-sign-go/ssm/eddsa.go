package ssm

import (
	"crypto/ed25519"
	"crypto/rand"
	"fmt"
	"github.com/status-im/keycard-go/hexutils"
)

type EddsaSigner struct {
}

func (e *EddsaSigner) Sign(privateKeyHex string, messageHex string) (string, error) {
	privateKeyBytes := hexutils.HexToBytes(privateKeyHex)
	messageBytes := hexutils.HexToBytes(messageHex)
	signature := ed25519.Sign(privateKeyBytes, messageBytes)
	return hexutils.BytesToHex(signature), nil
}

func (e *EddsaSigner) GenerateKeyPair() (*Keypair, error) {
	publicKey, privateKey, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		return nil, err
	}

	return &Keypair{
		PublicKey:           hexutils.BytesToHex(publicKey),
		CompressedPublicKey: hexutils.BytesToHex(publicKey),
		PrivateKey:          hexutils.BytesToHex(privateKey),
	}, nil
}

func (e *EddsaSigner) VerifySignature(publicKeyHex string, messageHashHex string, signatureHex string) (bool, error) {
	// 1. 将十六进制字符串公钥解码为字节数组
	publicKeyBytes := hexutils.HexToBytes(publicKeyHex)
	if len(publicKeyBytes) != ed25519.PublicKeySize {
		return false, fmt.Errorf("无效的公钥长度: 期望 %d 字节，实际 %d 字节", ed25519.PublicKeySize, len(publicKeyBytes))
	}

	// 2. 将十六进制字符串消息解码为字节数组
	messageBytes := hexutils.HexToBytes(messageHashHex)

	// 3. 将十六进制字符串签名解码为字节数组
	signatureBytes := hexutils.HexToBytes(signatureHex)
	if len(signatureBytes) != ed25519.SignatureSize {
		return false, fmt.Errorf("无效的签名长度: 期望 %d 字节，实际 %d 字节", ed25519.SignatureSize, len(signatureBytes))
	}

	// 4. 验证签名
	return ed25519.Verify(publicKeyBytes, messageBytes, signatureBytes), nil
}
