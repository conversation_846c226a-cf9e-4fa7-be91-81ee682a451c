package main

import (
	"fmt"
	"github.com/ethereum/go-ethereum/log"
	"github.com/urfave/cli/v2"
	"wallet-sign-common/flags"
	"wallet-sign-go/config"
	"wallet-sign-go/service"
)

func NewCli() *cli.App {
	return &cli.App{
		Name:    "wallet-sign-go",
		Version: "0.0.1",
		Commands: []*cli.Command{
			{
				Name:   "rpc",
				Action: runRpc,
				Flags: []cli.Flag{
					flags.Config,
				},
			},
		},
	}
}

func runRpc(ctx *cli.Context) error {
	cfg, err := config.NewConfig(ctx.String(flags.Config.Name))
	if err != nil {
		return err
	}

	log.Info("config", "config", fmt.Sprintf("%+v", cfg))
	server, err := service.NewRpcServer(cfg)
	if err != nil {
		return err
	}

	return server.Start()
}
