package basic

import (
	"context"
	"encoding/hex"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/log"
	"math/big"
	"wallet-sign-account/config"
	"wallet-sign-account/protobuf/generated"
)

type Basic struct {
	config *config.NodeConfig
	client *ethclient.Client
	pb.UnimplementedServiceServer
}

func New(config *config.NodeConfig) (*Basic, error) {
	client, err := ethclient.Dial(config.RpcUrl)
	if err != nil {
		return nil, err
	}

	return &Basic{
		config: config,
		client: client,
	}, nil
}

func (b *Basic) GetBlockByNumber(ctx context.Context, request *pb.GetBlockByNumberRequest) (*pb.GetBlockByNumberResponse, error) {
	// 将 uint64 转换为 *big.Int
	blockNumber := big.NewInt(int64(request.Number))

	block, err := b.client.BlockByNumber(ctx, blockNumber)
	if err != nil {
		return nil, err
	}

	return &pb.GetBlockByNumberResponse{
		Number:     block.Number().Uint64(),
		Hash:       block.Hash().Hex(),
		ParentHash: block.ParentHash().Hex(),
		Header: &pb.ResponseHeader{
			Code:    pb.ReturnCode_SUCCESS,
			Message: "",
		},
	}, nil
}

func (b *Basic) PublicKeyToAddress(ctx context.Context, req *pb.PublicKeyToAddressRequest) (*pb.PublicKeyToAddressResponse, error) {
	// 解码十六进制格式的公钥
	publicKeyBytes, err := hex.DecodeString(req.PublicKey)
	if err != nil {
		log.Error("decode public key failed:", err)
		return &pb.PublicKeyToAddressResponse{
			Header: &pb.ResponseHeader{
				Code:    pb.ReturnCode_ERROR,
				Message: "decode public key failed",
			},
			Address: "",
		}, nil
	}

	// 使用以太坊地址生成算法：Keccak256哈希的后20字节
	// 注意：跳过公钥的第一个字节（压缩标识符）
	address := common.BytesToAddress(crypto.Keccak256(publicKeyBytes[1:])[12:])
	return &pb.PublicKeyToAddressResponse{
		Header: &pb.ResponseHeader{
			Code:    pb.ReturnCode_SUCCESS,
			Message: "decode public key success",
		},
		Address: address.String(),
	}, nil
}
