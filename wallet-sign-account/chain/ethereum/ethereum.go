package ethereum

import (
	"wallet-sign-account/chain/basic"
	"wallet-sign-account/config"
	"wallet-sign-account/protobuf/generated"
)

func GetName() string {
	return "ethereum"
}

type Ethereum struct {
	*basic.Basic
}

func New(config *config.NodeConfig) (pb.ServiceServer, error) {
	server, err := basic.New(config)
	if err != nil {
		return nil, err
	}

	return &Ethereum{
		Basic: server,
	}, nil
}
