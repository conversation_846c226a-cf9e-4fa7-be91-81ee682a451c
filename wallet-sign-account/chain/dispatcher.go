package chain

import (
	"context"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/log"
	"github.com/samber/lo"
	"sync"
	"wallet-sign-account/chain/ethereum"
	"wallet-sign-account/chain/optimism"
	"wallet-sign-account/config"
	"wallet-sign-account/protobuf/generated"
)

var chainFactoryMap = map[string]func(*config.NodeConfig) (pb.ServiceServer, error){
	ethereum.GetName(): ethereum.New,
	optimism.GetName(): optimism.New,
}

var chainMap = sync.Map{} //map[string]pb.ServiceServer

type Dispatcher struct {
	config          *config.Config
	supportedChains map[string]string
	pb.UnimplementedServiceServer
}

func NewDispatcher(cfg *config.Config) (*Dispatcher, error) {
	err := cfg.ValidateChains(lo.Keys(chainFactoryMap))
	if err != nil {
		return nil, err
	}

	unenabledChains := lo.FilterMapToSlice(cfg.NodesMap, func(name string, nodeConfig *config.NodeConfig) (string, bool) {
		return name, !nodeConfig.Enable
	})

	enabledChains := lo.FilterMapToSlice(cfg.NodesMap, func(name string, nodeConfig *config.NodeConfig) (string, bool) {
		return name, nodeConfig.Enable
	})

	log.Info("链已启用", "chains", enabledChains)

	if len(unenabledChains) > 0 {
		log.Warn("链未启用", "chains", unenabledChains)
	}

	var supportedChains = lo.KeyBy(
		lo.Filter(lo.Keys(chainFactoryMap),
			func(name string, index int) bool {
				return cfg.NodesMap[name].Enable
			}),
		func(name string) string {
			return name
		},
	)

	return &Dispatcher{
		config:          cfg,
		supportedChains: supportedChains,
	}, nil
}

func (d *Dispatcher) factory(name string) (pb.ServiceServer, error) {
	if value, ok := chainMap.Load(name); ok {
		if !lo.HasKey(d.supportedChains, name) {
			return nil, errors.New(fmt.Sprintf("chain %s is not enabled", name))
		}

		return value.(pb.ServiceServer), nil
	}

	if factory, ok := chainFactoryMap[name]; ok {
		server, err := factory(d.config.NodesMap[name])
		if err != nil {
			return nil, err
		}

		chainMap.Store(name, server)
		return server, nil
	}

	return nil, errors.New(fmt.Sprintf("chain %s is not implemented", name))
}

func (d *Dispatcher) GetSupportChains(context context.Context, req *pb.GetSupportChainsRequest) (*pb.GetSupportChainsResponse, error) {
	return &pb.GetSupportChainsResponse{
		Header: &pb.ResponseHeader{
			Code:    pb.ReturnCode_SUCCESS,
			Message: "",
		},
		Chains: lo.Keys(d.supportedChains),
	}, nil
}

func (d *Dispatcher) GetBlockByNumber(context context.Context, req *pb.GetBlockByNumberRequest) (*pb.GetBlockByNumberResponse, error) {
	instance, err := d.factory(req.Header.Chain)
	if err != nil {
		return &pb.GetBlockByNumberResponse{
			Header: &pb.ResponseHeader{
				Code:    pb.ReturnCode_ERROR,
				Message: err.Error(),
			},
		}, nil
	}

	return instance.GetBlockByNumber(context, req)
}

func (d *Dispatcher) PublicKeyToAddress(context context.Context, req *pb.PublicKeyToAddressRequest) (*pb.PublicKeyToAddressResponse, error) {
	instance, err := d.factory(req.Header.Chain)
	if err != nil {
		return &pb.PublicKeyToAddressResponse{
			Header: &pb.ResponseHeader{
				Code:    pb.ReturnCode_ERROR,
				Message: err.Error(),
			},
		}, nil
	}

	return instance.PublicKeyToAddress(context, req)
}
