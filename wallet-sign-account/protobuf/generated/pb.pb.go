// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pb.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReturnCode int32

const (
	ReturnCode_RETURN_CODE_UNSPECIFIED ReturnCode = 0
	ReturnCode_ERROR                   ReturnCode = 1
	ReturnCode_SUCCESS                 ReturnCode = 2
)

// Enum value maps for ReturnCode.
var (
	ReturnCode_name = map[int32]string{
		0: "RETURN_CODE_UNSPECIFIED",
		1: "ERROR",
		2: "SUCCESS",
	}
	ReturnCode_value = map[string]int32{
		"RETURN_CODE_UNSPECIFIED": 0,
		"ERROR":                   1,
		"SUCCESS":                 2,
	}
)

func (x ReturnCode) Enum() *ReturnCode {
	p := new(ReturnCode)
	*p = x
	return p
}

func (x ReturnCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReturnCode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_proto_enumTypes[0].Descriptor()
}

func (ReturnCode) Type() protoreflect.EnumType {
	return &file_pb_proto_enumTypes[0]
}

func (x ReturnCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReturnCode.Descriptor instead.
func (ReturnCode) EnumDescriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{0}
}

// 所有请求通用的参数
type RequestHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	BearerToken   string                 `protobuf:"bytes,2,opt,name=bearer_token,json=bearerToken,proto3" json:"bearer_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestHeader) Reset() {
	*x = RequestHeader{}
	mi := &file_pb_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestHeader) ProtoMessage() {}

func (x *RequestHeader) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestHeader.ProtoReflect.Descriptor instead.
func (*RequestHeader) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{0}
}

func (x *RequestHeader) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *RequestHeader) GetBearerToken() string {
	if x != nil {
		return x.BearerToken
	}
	return ""
}

// 所有响应通用的参数
type ResponseHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          ReturnCode             `protobuf:"varint,1,opt,name=code,proto3,enum=ReturnCode" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseHeader) Reset() {
	*x = ResponseHeader{}
	mi := &file_pb_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseHeader) ProtoMessage() {}

func (x *ResponseHeader) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseHeader.ProtoReflect.Descriptor instead.
func (*ResponseHeader) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{1}
}

func (x *ResponseHeader) GetCode() ReturnCode {
	if x != nil {
		return x.Code
	}
	return ReturnCode_RETURN_CODE_UNSPECIFIED
}

func (x *ResponseHeader) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetBlockByNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *RequestHeader         `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Number        uint64                 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
	IncludeTxs    bool                   `protobuf:"varint,3,opt,name=include_txs,json=includeTxs,proto3" json:"include_txs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockByNumberRequest) Reset() {
	*x = GetBlockByNumberRequest{}
	mi := &file_pb_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockByNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockByNumberRequest) ProtoMessage() {}

func (x *GetBlockByNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockByNumberRequest.ProtoReflect.Descriptor instead.
func (*GetBlockByNumberRequest) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{2}
}

func (x *GetBlockByNumberRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBlockByNumberRequest) GetNumber() uint64 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *GetBlockByNumberRequest) GetIncludeTxs() bool {
	if x != nil {
		return x.IncludeTxs
	}
	return false
}

type GetBlockByNumberResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *ResponseHeader        `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Number        uint64                 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	ParentHash    string                 `protobuf:"bytes,4,opt,name=parent_hash,json=parentHash,proto3" json:"parent_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockByNumberResponse) Reset() {
	*x = GetBlockByNumberResponse{}
	mi := &file_pb_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockByNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockByNumberResponse) ProtoMessage() {}

func (x *GetBlockByNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockByNumberResponse.ProtoReflect.Descriptor instead.
func (*GetBlockByNumberResponse) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{3}
}

func (x *GetBlockByNumberResponse) GetHeader() *ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBlockByNumberResponse) GetNumber() uint64 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *GetBlockByNumberResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *GetBlockByNumberResponse) GetParentHash() string {
	if x != nil {
		return x.ParentHash
	}
	return ""
}

type GetSupportChainsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *RequestHeader         `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportChainsRequest) Reset() {
	*x = GetSupportChainsRequest{}
	mi := &file_pb_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportChainsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportChainsRequest) ProtoMessage() {}

func (x *GetSupportChainsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportChainsRequest.ProtoReflect.Descriptor instead.
func (*GetSupportChainsRequest) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{4}
}

func (x *GetSupportChainsRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetSupportChainsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *ResponseHeader        `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Chains        []string               `protobuf:"bytes,2,rep,name=chains,proto3" json:"chains,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportChainsResponse) Reset() {
	*x = GetSupportChainsResponse{}
	mi := &file_pb_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportChainsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportChainsResponse) ProtoMessage() {}

func (x *GetSupportChainsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportChainsResponse.ProtoReflect.Descriptor instead.
func (*GetSupportChainsResponse) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{5}
}

func (x *GetSupportChainsResponse) GetHeader() *ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetSupportChainsResponse) GetChains() []string {
	if x != nil {
		return x.Chains
	}
	return nil
}

type PublicKeyToAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *RequestHeader         `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	PublicKey     string                 `protobuf:"bytes,2,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicKeyToAddressRequest) Reset() {
	*x = PublicKeyToAddressRequest{}
	mi := &file_pb_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKeyToAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKeyToAddressRequest) ProtoMessage() {}

func (x *PublicKeyToAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKeyToAddressRequest.ProtoReflect.Descriptor instead.
func (*PublicKeyToAddressRequest) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{6}
}

func (x *PublicKeyToAddressRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PublicKeyToAddressRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type PublicKeyToAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *ResponseHeader        `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Address       string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicKeyToAddressResponse) Reset() {
	*x = PublicKeyToAddressResponse{}
	mi := &file_pb_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicKeyToAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicKeyToAddressResponse) ProtoMessage() {}

func (x *PublicKeyToAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicKeyToAddressResponse.ProtoReflect.Descriptor instead.
func (*PublicKeyToAddressResponse) Descriptor() ([]byte, []int) {
	return file_pb_proto_rawDescGZIP(), []int{7}
}

func (x *PublicKeyToAddressResponse) GetHeader() *ResponseHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PublicKeyToAddressResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_pb_proto protoreflect.FileDescriptor

const file_pb_proto_rawDesc = "" +
	"\n" +
	"\bpb.proto\"H\n" +
	"\rRequestHeader\x12\x14\n" +
	"\x05chain\x18\x01 \x01(\tR\x05chain\x12!\n" +
	"\fbearer_token\x18\x02 \x01(\tR\vbearerToken\"K\n" +
	"\x0eResponseHeader\x12\x1f\n" +
	"\x04code\x18\x01 \x01(\x0e2\v.ReturnCodeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"z\n" +
	"\x17GetBlockByNumberRequest\x12&\n" +
	"\x06header\x18\x01 \x01(\v2\x0e.RequestHeaderR\x06header\x12\x16\n" +
	"\x06number\x18\x02 \x01(\x04R\x06number\x12\x1f\n" +
	"\vinclude_txs\x18\x03 \x01(\bR\n" +
	"includeTxs\"\x90\x01\n" +
	"\x18GetBlockByNumberResponse\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.ResponseHeaderR\x06header\x12\x16\n" +
	"\x06number\x18\x02 \x01(\x04R\x06number\x12\x12\n" +
	"\x04hash\x18\x03 \x01(\tR\x04hash\x12\x1f\n" +
	"\vparent_hash\x18\x04 \x01(\tR\n" +
	"parentHash\"A\n" +
	"\x17GetSupportChainsRequest\x12&\n" +
	"\x06header\x18\x01 \x01(\v2\x0e.RequestHeaderR\x06header\"[\n" +
	"\x18GetSupportChainsResponse\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.ResponseHeaderR\x06header\x12\x16\n" +
	"\x06chains\x18\x02 \x03(\tR\x06chains\"b\n" +
	"\x19PublicKeyToAddressRequest\x12&\n" +
	"\x06header\x18\x01 \x01(\v2\x0e.RequestHeaderR\x06header\x12\x1d\n" +
	"\n" +
	"public_key\x18\x02 \x01(\tR\tpublicKey\"_\n" +
	"\x1aPublicKeyToAddressResponse\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.ResponseHeaderR\x06header\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress*A\n" +
	"\n" +
	"ReturnCode\x12\x1b\n" +
	"\x17RETURN_CODE_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05ERROR\x10\x01\x12\v\n" +
	"\aSUCCESS\x10\x022\xf0\x01\n" +
	"\aService\x12I\n" +
	"\x10GetSupportChains\x12\x18.GetSupportChainsRequest\x1a\x19.GetSupportChainsResponse\"\x00\x12I\n" +
	"\x10GetBlockByNumber\x12\x18.GetBlockByNumberRequest\x1a\x19.GetBlockByNumberResponse\"\x00\x12O\n" +
	"\x12PublicKeyToAddress\x12\x1a.PublicKeyToAddressRequest\x1a\x1b.PublicKeyToAddressResponse\"\x00B\x19Z\x17./protobuf/generated;pbb\x06proto3"

var (
	file_pb_proto_rawDescOnce sync.Once
	file_pb_proto_rawDescData []byte
)

func file_pb_proto_rawDescGZIP() []byte {
	file_pb_proto_rawDescOnce.Do(func() {
		file_pb_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_proto_rawDesc), len(file_pb_proto_rawDesc)))
	})
	return file_pb_proto_rawDescData
}

var file_pb_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_proto_goTypes = []any{
	(ReturnCode)(0),                    // 0: ReturnCode
	(*RequestHeader)(nil),              // 1: RequestHeader
	(*ResponseHeader)(nil),             // 2: ResponseHeader
	(*GetBlockByNumberRequest)(nil),    // 3: GetBlockByNumberRequest
	(*GetBlockByNumberResponse)(nil),   // 4: GetBlockByNumberResponse
	(*GetSupportChainsRequest)(nil),    // 5: GetSupportChainsRequest
	(*GetSupportChainsResponse)(nil),   // 6: GetSupportChainsResponse
	(*PublicKeyToAddressRequest)(nil),  // 7: PublicKeyToAddressRequest
	(*PublicKeyToAddressResponse)(nil), // 8: PublicKeyToAddressResponse
}
var file_pb_proto_depIdxs = []int32{
	0,  // 0: ResponseHeader.code:type_name -> ReturnCode
	1,  // 1: GetBlockByNumberRequest.header:type_name -> RequestHeader
	2,  // 2: GetBlockByNumberResponse.header:type_name -> ResponseHeader
	1,  // 3: GetSupportChainsRequest.header:type_name -> RequestHeader
	2,  // 4: GetSupportChainsResponse.header:type_name -> ResponseHeader
	1,  // 5: PublicKeyToAddressRequest.header:type_name -> RequestHeader
	2,  // 6: PublicKeyToAddressResponse.header:type_name -> ResponseHeader
	5,  // 7: Service.GetSupportChains:input_type -> GetSupportChainsRequest
	3,  // 8: Service.GetBlockByNumber:input_type -> GetBlockByNumberRequest
	7,  // 9: Service.PublicKeyToAddress:input_type -> PublicKeyToAddressRequest
	6,  // 10: Service.GetSupportChains:output_type -> GetSupportChainsResponse
	4,  // 11: Service.GetBlockByNumber:output_type -> GetBlockByNumberResponse
	8,  // 12: Service.PublicKeyToAddress:output_type -> PublicKeyToAddressResponse
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_pb_proto_init() }
func file_pb_proto_init() {
	if File_pb_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_proto_rawDesc), len(file_pb_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_proto_goTypes,
		DependencyIndexes: file_pb_proto_depIdxs,
		EnumInfos:         file_pb_proto_enumTypes,
		MessageInfos:      file_pb_proto_msgTypes,
	}.Build()
	File_pb_proto = out.File
	file_pb_proto_goTypes = nil
	file_pb_proto_depIdxs = nil
}
