syntax = "proto3";
option go_package="./protobuf/generated;pb";

/**
公共的请求部分
chain
token
设置在上下文中
 */

service Service {
  // 获取支持的链
  rpc GetSupportChains(GetSupportChainsRequest) returns (GetSupportChainsResponse) {}
  // 获取区块信息
  rpc GetBlockByNumber(GetBlockByNumberRequest) returns (GetBlockByNumberResponse) {}
  // 公钥转地址
  rpc PublicKeyToAddress(PublicKeyToAddressRequest) returns(PublicKeyToAddressResponse){}
}

enum ReturnCode {
  RETURN_CODE_UNSPECIFIED = 0;
  ERROR = 1;
  SUCCESS = 2;
}

// 所有请求通用的参数
message RequestHeader {
    string chain = 1;
    string bearer_token = 2;
}

// 所有响应通用的参数
message ResponseHeader {
  ReturnCode code = 1;
  string message = 2;
}

message GetBlockByNumberRequest {
  RequestHeader header = 1;
  uint64 number = 2;
  bool   include_txs = 3;
}
message GetBlockByNumberResponse {
  ResponseHeader header = 1;
  uint64 number = 2;
  string hash = 3;
  string parent_hash = 4;
}

message GetSupportChainsRequest {
  RequestHeader header = 1;
}
message GetSupportChainsResponse {
  ResponseHeader header = 1;
  repeated string chains = 2;
}

message PublicKeyToAddressRequest {
  RequestHeader header = 1;
  string public_key = 2;
}
message PublicKeyToAddressResponse {
  ResponseHeader header = 1;
  string address = 2;
}
