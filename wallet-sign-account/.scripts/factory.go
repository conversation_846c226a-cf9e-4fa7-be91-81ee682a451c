package chain_1

import (
	"errors"
	"fmt"
	"github.com/samber/lo"
	"slices"
	"wallet-sign-account/chain/ethereum"
	"wallet-sign-account/chain/optimism"
	"wallet-sign-account/config"
	"wallet-sign-account/protobuf/pb"
)

type Factory struct {
	supportedChainMap map[string]pb.ServiceServer
	enabledChains     map[string]struct{}
}

func NewFactory(cfg *config.Config) (*Factory, error) {
	// 支持的链服务 map
	var supportedChainMap = make(map[string]pb.ServiceServer)
	// 支持的链构造函数 map
	var supportedChainFactoryMap = map[string]func(*config.NodeConfig) (pb.ServiceServer, error){
		ethereum.GetName(): ethereum.New,
		optimism.GetName(): optimism.New,
	}
	// 激活的链
	var enabledChains = make(map[string]struct{})

	err := validateConfig(cfg, lo.Keys(supportedChainFactoryMap))
	if err != nil {
		return nil, err
	}

	// 初始化所有支持的链
	for name, newChainFunc := range supportedChainFactoryMap {
		if cfg.NodesMap[name].Enable {
			enabledChains[name] = struct{}{}
		}

		ch, err := newChainFunc(cfg.NodesMap[name])

		if err != nil {
			return nil, err
		}

		supportedChainMap[name] = ch
	}

	return &Factory{
		supportedChainMap: supportedChainMap,
		enabledChains:     enabledChains,
	}, nil
}

func (f *Factory) Get(name string) (pb.ServiceServer, error) {
	if !lo.HasKey(f.enabledChains, name) {
		return nil, errors.New(fmt.Sprintf("chain %s not enabled", name))
	}

	if ch, ok := f.supportedChainMap[name]; !ok {
		return nil, errors.New(fmt.Sprintf("chain %s not supported", name))
	} else {
		return ch.(pb.ServiceServer), nil
	}
}

func (f *Factory) GetEnabledChains() []string {
	return lo.Keys(f.enabledChains)
}

func validateConfig(config *config.Config, supportedChains []string) error {
	cfgChains := lo.Keys(config.NodesMap)

	unsupportedChains := lo.Filter(cfgChains, func(name string, index int) bool {
		return !slices.Contains(supportedChains, name)
	})

	unconfiguredChains := lo.Filter(supportedChains, func(name string, index int) bool {
		return !slices.Contains(cfgChains, name)
	})

	var err error
	if len(unsupportedChains) > 0 {
		err = errors.New(fmt.Sprintf("配置文件中缺少链的配置: %+v", unconfiguredChains))
	}

	if len(unconfiguredChains) > 0 {
		err = errors.Join(err, errors.New(fmt.Sprintf("配置文件中指定了不支持的链: %+v", unsupportedChains)))
	}

	return err
}
