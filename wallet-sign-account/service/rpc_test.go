package service_test

import (
	"context"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"log"
	"testing"
	"wallet-sign-account/protobuf/generated"
)

var client pb.ServiceClient

func init() {
	// 尝试使用 TLS 连接
	creds, err := credentials.NewClientTLSFromFile("../certs/server.crt", "")
	if err != nil {
		// 如果证书文件不存在，使用不安全连接
		log.Printf("Warning: TLS cert not found, using insecure connection: %v", err)
		conn, err := grpc.NewClient("localhost:8080",
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		)
		if err != nil {
			log.Fatalf("Failed to connect with insecure: %v", err)
		}
		client = pb.NewServiceClient(conn)
		return
	}

	// 使用 TLS 连接
	conn, err := grpc.NewClient("localhost:8080",
		grpc.WithTransportCredentials(creds),
	)
	if err != nil {
		log.Fatalf("Failed to connect with TLS: %v", err)
	}

	client = pb.NewServiceClient(conn)
}

func TestRpcServer_GetSupportChains(t *testing.T) {
	chains, err := client.GetSupportChains(context.Background(), &pb.GetSupportChainsRequest{})
	assert.NoError(t, err)
	t.Log(chains.Chains)
	assert.GreaterOrEqual(t, len(chains.Chains), 1)
}

func TestRpcServer_GetBlockByNumber(t *testing.T) {
	res, err := client.GetBlockByNumber(context.Background(), &pb.GetBlockByNumberRequest{
		Header: &pb.RequestHeader{
			Chain: "ethereum",
		},
		Number: 1,
	})

	assert.NoError(t, err)
	assert.Equal(t, uint64(1), res.Number)
}
