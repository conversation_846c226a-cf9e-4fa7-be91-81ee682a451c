package service

import (
	"google.golang.org/grpc"
	"wallet-sign-account/chain"
	"wallet-sign-account/config"
	"wallet-sign-account/protobuf/generated"
	"wallet-sign-common/rpc"
)

type RpcService struct {
	config     *config.Config
	dispatcher *chain.Dispatcher
}

func (r *RpcService) Start() error {
	return rpc.StartServer(&r.config.Rpc, func(s *grpc.Server) error {
		pb.RegisterServiceServer(s, r.dispatcher)
		return nil
	})
}

func NewRpcService(config *config.Config) (*RpcService, error) {
	dispatcher, err := chain.NewDispatcher(config)
	if err != nil {
		return nil, err
	}

	return &RpcService{
		config:     config,
		dispatcher: dispatcher,
	}, nil
}
